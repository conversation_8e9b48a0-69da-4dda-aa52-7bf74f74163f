//
//  FNFreshMrFreshFeedsGoodsViewController.m
//  FNFresh
//
//  Created by wa<PERSON><PERSON> on 2019/5/17.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshFeedsGoodsViewController.h"
#import "FNFreshMrFreshFeedsGoodsCollectionViewCell.h"
#import "FNFreshMrFreshFeedsFoodsMenuCell.h"
#import "FNFreshMrFreshFeedsGifPitLocationCell.h"
#import "FNFreshMrFreshFeedsTabResponseModel.h"
#import "FNFreshMrFreshfeedsgoodsParameterModel.h"
#import "FNFreshMrFreshFeedsGoodsResponseModel.h"
#import "FNFreshMrFreshFeedsMonthlyTicketResponseModel.h"
#import "FNFreshMJRefreshFooter.h"
#import "FNFreshHomeFeedsMJFooter.h"
#import "FNFreshGoodsNegativeFeedbackView.h"
#import "FNFreshMrFreshService.h"
#import "FNFreshNormalRefreshHeader.h"
#import "UIViewController+Loading.h"
#import "FNFreshTabBarController.h"
#import "FNMediator+FNFreshMerchandiseDetail.h"
#import "FNFreshUrlRouter.h"
#import "UIImage+GIF.h"
#import "FNMrFreshReadAfterRecommendResponseModel.h"
#import "FNFreshMenuDetailViewController.h"
#import "FNFreshRecommendService.h"
#import "FNFreshRecommendResponseModel.h"
#import "FNFreshMrFreshFeedsGoodsCollectionFlowLayout.h"
#import "FNFreshMrFreshCollectionViewFlowLayout.h"
#import "FNFreshMrFreshMonthlyTicketCell.h"
#import "FNMediator+FNFreshMonthCardModule.h"
#import "FNFreshMrFreshFeedsADRotationCell.h"
#import "UIResponder+FNHomeEvent.h"
#import "FNFreshShopCartSimilarListController.h"
#import "FNFreshMrFreshSingleFeedsGoodsVC.h"
#import "UIViewController+FNFreshBundleHandler.h"
#import "FNFreshMrFreshFeedsEnterParameterModel.h"
#import "FNFreshMrFreshFeedsEnterResponseModel.h"
#import "FNFreshMrFreshFeedsCollectionViewCell.h"
#import "FNFreshMrFreshViewController.h"
#import "FNFreshMrFreshWaterfallFlowHeadReusableView.h"
#import <FNTagLabel/FNTagImageManager.h>
#import "FNFreshMrFreshBestMatchCollectionViewCell.h"
#import "FNFreshMrFreshGuessLikeCollectionViewCell.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"

static NSString * const FNFreshMrFreshFeedsGoodsCollectionViewCellIdentifier =
@"FNFreshMrFreshFeedsGoodsCollectionViewCell";

static NSString * const FNFreshMrFreshFeedsFoodsMenuCellIdentifier =
@"FNFreshMrFreshFeedsFoodsMenuCell";

static NSString * const FNFreshMrFreshFeedsGifPitLocationCellIdentifier =
@"FNFreshMrFreshFeedsGifPitLocationCell";

static NSString * const FNFreshMrFreshMonthlyTicketCellIdentifier =
@"FNFreshMrFreshMonthlyTicketCell";

static NSString * const FNFreshMrFreshFeedsADRotationCellIdentifier =
@"FNFreshMrFreshFeedsADRotationCell";

static NSString * const FNFreshMrFreshGuessLikeCollectionViewCellIdentifier =
@"FNFreshMrFreshGuessLikeCollectionViewCell";

static NSString * const FNFreshMrFreshBestMatchCollectionViewCellIdentifier =
@"FNFreshMrFreshBestMatchCollectionViewCell";

static NSInteger const page_size = 20;

@interface FNFreshMrFreshFeedsGoodsViewController ()
<UIScrollViewDelegate,UICollectionViewDelegateFlowLayout>

@property (copy, nonatomic) NSString *abtest;
@property (copy, nonatomic) NSString *sort;
@property (copy, nonatomic) NSString *poolType;
@property (assign, nonatomic) NSInteger picCount;
@property (assign, nonatomic) NSInteger goodsCount;
@property (assign, nonatomic) NSInteger pageIndex;
@property (assign, nonatomic) NSInteger recommendByCmsSize; // 推荐人工品数量（透传用）
@property (assign, nonatomic) NSInteger recommendByCmsPosition; // 推荐人工品位置（透传用）
@property (assign, nonatomic) NSInteger thisPagePicCount;
@property (assign, nonatomic) NSInteger timeRecommendPosition;// （透传用）

@property (strong, nonatomic) FNFreshMrFreshFeedsGoodsResponseModel *responseModel;
@property (strong, nonatomic) NSMutableArray *goodsList;
@property (nonatomic,assign, getter=isPlayGif) BOOL playGif;
@property (assign, nonatomic) BOOL isMenu;  // 菜谱页
@property (strong, nonatomic) NSMutableDictionary<NSString *, UICollectionViewCell *> *visibleGoodsDic;
@property (assign, nonatomic) BOOL canScroll;
@property (strong, nonatomic) FNFreshMrFreshFeedsGoodsCollectionViewCell *temCell;

@property (assign, nonatomic) BOOL hasAddMonthlyTicket;
/// 负反馈引导页展示所在位置indexPath.row
@property (assign, nonatomic) NSInteger negativeGuidanceItemIndex;
@property (assign, nonatomic) BOOL isShowNegativeGuidance;
@property (assign, nonatomic) BOOL isJumpToNextTab;
@property (copy, nonatomic) NSString *homePageId;

/**
 * 用户浏览请求本页时传递的最新加购货号list
 */
@property (copy, nonatomic) NSArray<NSString *> * thisPageSkuCodes;

@end

@implementation FNFreshMrFreshFeedsGoodsViewController

-(FNFreshMrFreshFeedsGoodsCollectionViewCell *)temCell {

    if (!_temCell) {

        _temCell = [[UINib nibWithNibName:@"FNFreshMrFreshFeedsGoodsCollectionViewCell" bundle:nil]
                    instantiateWithOwner:self options:nil].lastObject;

    }
    return _temCell;
}

#pragma mark - Lifecycle funcs
-(instancetype)init{

    UICollectionViewFlowLayout *layout = [[FNFreshMrFreshFeedsGoodsCollectionFlowLayout alloc] init];
    
    return [self initWithCollectionViewLayout:layout];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.pageIndex = 0;
    [self setup];
    //监听网络状态，gif只在wifi状态下加载播放
    [[AFNetworkReachabilityManager sharedManager] startMonitoring];
    [self fn_addObserver];
    [self initializeNetworkReachability];

    self.homePageId =
    [[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"] ? @"193" : @"3";
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self handleVisibleGoodsDic];
}

- (void)dealloc {
    [[AFNetworkReachabilityManager sharedManager] stopMonitoring];
    [self fn_removeObserver];
}

#pragma mark - notifications
- (void)fn_addObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkStateChanged:) name:AFNetworkingReachabilityDidChangeNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(shoppingCartUpdateNum) name:kMerchantListSkuNumsDidUpdate object:nil];
}

- (void)fn_removeObserver {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)shoppingCartUpdateNum {
    [self.collectionView reloadData];
}

- (void)networkStateChanged:(NSNotification *)notify {
    //若当前状态是wifi，则feed商品播放预览gif
    self.playGif = [AFNetworkReachabilityManager sharedManager].reachableViaWiFi;
    [self.collectionView reloadData];
}

- (void)initializeNetworkReachability {
    self.playGif = [AFNetworkReachabilityManager sharedManager].reachableViaWiFi;
}

#pragma mark - request functions

/// 请求商品/菜谱 数据
- (void)reqestFeedsGoods {
    if (self.isJumpToNextTab) {

        FNFreshMrFreshFeedsCollectionViewCell *cell =
        [self findSuperviewOfClass:[FNFreshMrFreshFeedsCollectionViewCell class]];
        if (cell) {
            // 获取当前的队列（可以是主队列或者其他队列）
            dispatch_queue_t currentQueue = dispatch_get_main_queue();
            
            // 设置延迟时间为 1 秒
            dispatch_time_t delayTime = dispatch_time(
                                                      DISPATCH_TIME_NOW,
                                                      (int64_t)(0.5 * NSEC_PER_SEC)
                                                      );

            // 使用 dispatch_after 函数延迟执行代码块
            dispatch_after(delayTime, currentQueue, ^{
                [cell selectIndex:self.index + 1];
                [[FNFreshTabBarController shareInstance].homeVC.fallFlowHeaderView 
                 selectIndex:self.index + 1];
            });
        }
        
        [self.collectionView.mj_footer endRefreshingWithNoMoreData];
        return;
    }

    if (self.pageIndex == 0) {
        [self.goodsList removeAllObjects];
        [self.visibleGoodsDic removeAllObjects];
        self.hasAddMonthlyTicket = NO;
        [self startListProgressWithBackgroundColor:[UIColor clearColor]];
    }

    FNFreshMrFreshFeedsTabModel *tabModel = [self.dataModel.feedsTabList safeObjectAtIndex:self.index];
    self.isMenu = tabModel.isMenu;
    FNFreshMrFreshFeedsGoodsParameterModel *model = 
    [[FNFreshMrFreshFeedsGoodsParameterModel alloc] init];

    model.moduleId = self.dataModel.moduleId;
    model.tabId = tabModel.tabId;
    model.storeId = [FNFreshUser shareInstance].shopId;
    model.title = tabModel.title;
    model.type = tabModel.type;
    model.goodsCount = self.goodsCount;
    model.picCount = self.picCount;
    model.poolType = self.poolType;
    model.sort = self.sort;
    model.pageSize = page_size;
    model.pageIndex = self.pageIndex == 0?1:self.pageIndex;
    model.isMenu = tabModel.isMenu;
    model.recommendByCmsSize = self.recommendByCmsSize;
    model.recommendByCmsPosition = self.recommendByCmsPosition;
    model.prePagePicCount = self.pageIndex == 0 ? 0 : self.thisPagePicCount;
    model.timeRecommendPosition = self.pageIndex == 0 ? 0 : self.timeRecommendPosition;
    if (self.index == 0) {
        FNFreshMrFreshUserBrowsingSkuModel *userSkuModel = [[FNFreshMrFreshUserBrowsingSkuModel alloc]
                                                            init];
        userSkuModel.prePageSkuCodes = self.pageIndex == 0 ? [NSArray array] : self.thisPageSkuCodes;
        userSkuModel.thisPageSkuCodes = [[FNFreshUtils shareInstance] getRecentBrowseOrAddToShopcartGoodsList];
        self.thisPageSkuCodes = userSkuModel.thisPageSkuCodes;
        // 精选
        model.userBrowsing = userSkuModel;
    }

    WS(weakSelf)
    [FNFreshMrFreshService requestFeedsGoodsWithParameter:model 
                                                  success:
     ^(FNFreshMrFreshFeedsGoodsResponseModel *responseObject, BOOL isCache) {

        [weakSelf stopProgress];
        [weakSelf initializeNetworkReachability];
        weakSelf.responseModel = responseObject;
        weakSelf.pageIndex = responseObject.nextPageIndex;
        weakSelf.abtest = responseObject.abtest;
        weakSelf.sort = responseObject.sort;
        weakSelf.poolType = responseObject.poolType;
        weakSelf.picCount = responseObject.picCount;
        weakSelf.goodsCount = responseObject.goodsCount;
        weakSelf.recommendByCmsSize = responseObject.recommendByCmsSize;
        weakSelf.recommendByCmsPosition = responseObject.recommendByCmsPosition;
        weakSelf.thisPagePicCount = responseObject.thisPagePicCount;
        weakSelf.timeRecommendPosition = responseObject.timeRecommendPosition;
        if (weakSelf.isMenu) {
            [weakSelf.goodsList addObjectsFromArray:responseObject.menuList];
            [weakSelf.collectionView reloadData];
        } else {
            [weakSelf handleGoodsList:responseObject.list];
        }
        
        // 鲜月票
        if (weakSelf.index == 0 && !weakSelf.hasAddMonthlyTicket) {
            [weakSelf requestFreshMonthlyTicket];
        }
        
        if (responseObject.nextPageIndex <= 0) {
            if (weakSelf.index == weakSelf.dataModel.feedsTabList.count - 1) {
                [weakSelf.collectionView.mj_footer endRefreshingWithNoMoreData];
                weakSelf.isJumpToNextTab = false;
            } else {
                // 这里跳到下一个tab
                [weakSelf.collectionView.mj_footer endRefreshingWithNoMoreData];
                weakSelf.isJumpToNextTab = true;
                ((FNFreshHomeFeedsMJFooter *)weakSelf.collectionView.mj_footer)
                    .canRefreshWithNoMoreData = YES;

                FNFreshMrFreshFeedsTabModel *tabModel = 
                [weakSelf.dataModel.feedsTabList safeObjectAtIndex:weakSelf.index + 1];
                NSString *nextTabName = [NSString stringWithFormat:@"上滑查看%@", tabModel.title];
                [((FNFreshHomeFeedsMJFooter *)weakSelf.collectionView.mj_footer) 
                 setTitle:nextTabName forState:MJRefreshStateNoMoreData];

                // 埋点
                [FNFreshAgent eventWithTrackDataPrameters: @{
                    @"page_col":@"194001",
                    @"page_id":weakSelf.homePageId,
                    @"track_type":@"6",
                    @"col_position":[NSString stringWithFormat:@"%ld", weakSelf.index + 1],
                }];
            }
            
        } else {
            [weakSelf.collectionView.mj_footer endRefreshing];
            weakSelf.isJumpToNextTab = false;
        }
        [weakSelf.collectionView.mj_header endRefreshing];
        
    } failure:^(id responseObject, NSError *error) {

        [weakSelf stopProgress];
        [weakSelf.collectionView.mj_header endRefreshing];

        if (weakSelf.index == weakSelf.dataModel.feedsTabList.count - 1) {

            [weakSelf.collectionView.mj_footer endRefreshingWithNoMoreData];
            weakSelf.isJumpToNextTab = false;

        } else {

            // 这里跳到下一个tab
            [weakSelf.collectionView.mj_footer endRefreshingWithNoMoreData];
            weakSelf.isJumpToNextTab = true;
            ((FNFreshHomeFeedsMJFooter *)weakSelf.collectionView.mj_footer).canRefreshWithNoMoreData = YES;
            FNFreshMrFreshFeedsTabModel *tabModel = [weakSelf.dataModel.feedsTabList 
                                                     safeObjectAtIndex:weakSelf.index + 1];

            NSString *nextTabName = [NSString stringWithFormat:@"上滑查看%@", tabModel.title];
            [((FNFreshHomeFeedsMJFooter *)weakSelf.collectionView.mj_footer) 
             setTitle:nextTabName forState:MJRefreshStateNoMoreData];

            // 埋点
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col": @"194001",
                @"page_id": weakSelf.homePageId,
                @"track_type": @"6",
                @"col_position": [NSString stringWithFormat:@"%ld", weakSelf.index + 1],
            }];
        }
    }];

    // feeds流分页事件埋点
    NSInteger col_position = self.pageIndex == 0 ? 1 : self.pageIndex;
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"145007",
        @"page_id":self.homePageId,
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",col_position],
        @"remarks":[[NSString stringWithFormat:@"%ld",self.index + 1] jsonStringForKey:@"tabid"],
    }];

}

/**
 * 请求鲜月票模块
 */
- (void)requestFreshMonthlyTicket {

    FNFreshMrFreshFeedsGoodsParameterModel *para = [[FNFreshMrFreshFeedsGoodsParameterModel alloc] 
                                                    init];

    para.storeId = [FNFreshUser shareInstance].shopId;

    WS(weakSelf)
    [FNFreshMrFreshService requestFeedsMonthlyTicketWithParameter:para 
                                                          success:
     ^(FNFreshMrFreshFeedsMonthlyTicketResponseModel *responseObject, BOOL isCache) {

        BOOL hasEffect = 
        (!responseObject.subtitle || [responseObject.subtitle isEqualToString:@""]) &&
        responseObject.couponList.count >= 3;

        BOOL hasNoEffect = 
        responseObject.subtitle.length > 0 && responseObject.couponList.count >= 2;

        if (!hasEffect && !hasNoEffect) {
            return;
        }

        FNFreshMrFreshGoodsList *info = [[FNFreshMrFreshGoodsList alloc] init];
        info.flag = 3;
        info.monthlyTicket = responseObject;
        FNFreshMrFreshGoodsList *firstObj = [weakSelf.goodsList safeObjectAtIndex:0];
        NSInteger index = 0; // 如果第一个位置是商品，插入在商品位前，否则插入在第二个位置
        if (firstObj.flag != 0) {
            index = 1;
        }
        [weakSelf.goodsList insertObject:info atIndex:index];
        weakSelf.hasAddMonthlyTicket = YES;
        [weakSelf.collectionView reloadData];
        
    } failure:nil];
}

/**
 * 是否进入单页瀑布流
 */
- (void)requestEnterFeedsWaterFallWithGoods:(FNFreshMrFreshGoodsList *)goods {
//    [self simulatePreciseConflict];
//    return;
    [self startProgress];
    FNFreshMrFreshFeedsEnterParameterModel *para = [[FNFreshMrFreshFeedsEnterParameterModel alloc] init];
    para.store_id = [FNFreshUser shareInstance].shopId;
    para.goods_no = goods.goodsInfo.goodsID;
    para.tab_type = self.index == 0 ? 0 : 1;
    para.page_index = 1;
    WS(weakSelf)
    [FNFreshMrFreshService requestWhetherToEnterFeedsWithParameter:para success:
     ^(FNFreshMrFreshFeedsEnterResponseModel *responseObject, BOOL isCache) {
        [weakSelf stopProgress];
        if (responseObject.enterFeedsWaterFall && responseObject.goodsList.count >= 6) {
            // 进入单页feeds流页
            FNFreshMrFreshSingleFeedsGoodsVC *vc =
            [[FNFreshMrFreshSingleFeedsGoodsVC alloc] initWithGoodsId:goods.goodsInfo.goodsID
                                                         andFirstPage:responseObject.goodsList
                                                          hasNextPage:responseObject.hasNextPage
                                                              tabType:para.tab_type
                                                      deallocComplete:^{
                
                [weakSelf requestLookAfterRecommendDataWithcateSeq:goods.goodsInfo.cpSeq
                                                         goodsInfo:goods];
            }];
            [[FNFreshTabBarController shareInstance] pushViewController:vc animated:YES];
        } else {
            
            [weakSelf freshFeedsClickGoodsLookDetail:goods];
        }
    } failure:^(id responseObject, NSError *error) {
        [weakSelf stopProgress];
        [weakSelf freshFeedsClickGoodsLookDetail:goods];
    }];
    
}

// 使用GCD精确控制时序
- (void)simulatePreciseConflict {
    UIViewController *alertVC = [[UIViewController alloc] init];
    alertVC.view.backgroundColor = [UIColor redColor];
    UIViewController *detailVC = [[UIViewController alloc] init];
    detailVC.view.backgroundColor = [UIColor yellowColor];
    
    // 方案1：完全同步（纳秒级间隔）
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_HIGH, 0), ^{
        dispatch_sync(dispatch_get_main_queue(), ^{
            [self fn_presentViewController:alertVC
                         customAnimateType:FNFreshMrFreshCustomAnimateTypeFromBottom
                                  viewSize:[UIScreen mainScreen].bounds.size
                                  duration:0.55
                                     alpha:0.5
                                    handle:nil];
        });
        dispatch_sync(dispatch_get_main_queue(), ^{
            [[FNFreshTabBarController shareInstance] pushViewController:detailVC animated:YES];
        });
    });
    return;
    
//     方案2：微小延迟（模拟网络请求返回场景）
    dispatch_async(dispatch_get_main_queue(), ^{
        [self fn_presentViewController:alertVC
                     customAnimateType:FNFreshMrFreshCustomAnimateTypeFromBottom
                              viewSize:[UIScreen mainScreen].bounds.size
                              duration:0.55
                                 alpha:0.5
                                handle:nil];
    });
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 5 * NSEC_PER_MSEC), // 10毫秒
    dispatch_get_main_queue(), ^{
        [[FNFreshTabBarController shareInstance] pushViewController:detailVC animated:YES];
    });
}

#pragma mark - dataSource handler

/**
 * 用来处理可见cell gif 播放逻辑
 */
- (void)handleVisibleGoodsDic {
    // 排序
    NSArray *indexArr =
    [self.visibleGoodsDic.allKeys sortedArrayUsingComparator:
     ^NSComparisonResult(NSString *obj1, NSString *obj2) {
        NSNumber *num1 = [NSNumber numberWithInt:obj1.intValue];
        NSNumber *num2 = [NSNumber numberWithInt:obj2.intValue];
        NSComparisonResult result = [num1 compare:num2];
        return result == NSOrderedDescending;
    }];

    BOOL isHasChoose = NO;
    for (NSString *indexStr in indexArr) {
        FNFreshMrFreshGoodsList *model = [self.goodsList safeObjectAtIndex:indexStr.intValue];
        UICollectionViewCell *cell = [self.visibleGoodsDic objectForKey:indexStr];
        if ([cell isKindOfClass:[FNFreshMrFreshFeedsGoodsCollectionViewCell class]] && 
            !self.isShowNegativeGuidance && self.index == 0) {

            if ([FNFreshUser shareInstance].isLogin && 
                ![FNFreshUtils shareInstance].closeNegativeFeedbackGuidance &&
                ![FNFreshUtils shareInstance].shopcartNegativeFeedbackGuidance &&
                ![FNFreshUtils shareInstance].userCenterNegativeFeedbackGuidance) {

                [self.goodsList enumerateObjectsUsingBlock:
                 ^(FNFreshMrFreshGoodsList *obj, NSUInteger idx, BOOL * _Nonnull stop) {
                    if ([obj isEqual:model]) {
                        obj.isNegativeGuidance = YES;
                    }
                }];

                [(FNFreshMrFreshFeedsGoodsCollectionViewCell *)cell showFeedbackGuidance];
                self.isShowNegativeGuidance = YES;
            }
        }
        if (model.choosePlay) {
            if ([cell isKindOfClass:[FNFreshMrFreshFeedsGoodsCollectionViewCell class]]) {
                [(FNFreshMrFreshFeedsGoodsCollectionViewCell *)cell playVideo];
            }
            return;
        }
        if (isHasChoose) {
            if ([cell isKindOfClass:[FNFreshMrFreshFeedsGoodsCollectionViewCell class]]) {
                [(FNFreshMrFreshFeedsGoodsCollectionViewCell *)cell displayVideo];
            }
            continue;
        }
        if (model.flag == 0) {
            NSString *videoUrl = model.goodsInfo.imgUrl;
            BOOL isGif = [videoUrl hasSuffix:@".gif"];
            BOOL hasVideo = videoUrl.length > 0;
            if (hasVideo && isGif) {

                [self.goodsList enumerateObjectsUsingBlock:
                 ^(FNFreshMrFreshGoodsList *obj, NSUInteger idx, BOOL * _Nonnull stop) {

                    obj.choosePlay = NO;
                }];

                model.choosePlay = YES;
                isHasChoose = YES;
                if ([cell isKindOfClass:[FNFreshMrFreshFeedsGoodsCollectionViewCell class]]) {
                    [(FNFreshMrFreshFeedsGoodsCollectionViewCell *)cell playVideo];
                }
                continue;
            }
        }
    }
}

/**
 * 处理请求的goodsList
 * 这里提前异步加载tag
 * 防止第一次没有缓存导致cell自适应时发生tag展示不全现象
 */
- (void)handleGoodsList:(NSArray<FNFreshMrFreshGoodsList *> *)list {
    [self.goodsList addObjectsFromArray:list];

    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);

    for (FNFreshMrFreshGoodsList * item in list) {
        NSMutableArray *tags = [NSMutableArray array];
        if (item.goodsInfo.items.count > 0) {
            [item.goodsInfo.items enumerateObjectsUsingBlock:^(FNTag * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                [tags addObject:[self transformTag:obj]];
            }];
        }
        if ([item.goodsInfo.exchangeCardLabel safeObjectAtIndex:0]) {
            [tags addObject:[self transformTag:[item.goodsInfo.exchangeCardLabel safeObjectAtIndex:0]]];
        }
        if ([item.goodsInfo.deliveryTimeLabele safeObjectAtIndex:0]) {
            [tags addObject:[self transformTag:[item.goodsInfo.deliveryTimeLabele safeObjectAtIndex:0]]];
        }
        if (item.goodsInfo.weighCorner.count > 0) {
            // 单独计算称重标 一行过长展示不下……
            FNTag *tagModel = [item.goodsInfo.weighCorner safeObjectAtIndex:0];
            CGFloat width = (SCREEN_WIDTH - 33) / 2 - 18;
            tagModel.name = 
            [self nameStr:tagModel.name 
                     font:[UIFont systemFontOfSize:11]
               byMaxWidth:width 
                 hasToast:tagModel.toast.length > 0];

            [tags addObject:[self transformTag:tagModel]];
        }

        dispatch_group_async(group, queue, ^{
            [self cacheTags:tags completion:^{

            }];
        });

        dispatch_group_notify(group, dispatch_get_main_queue(), ^{
            [self.collectionView reloadData];
        });
    }
}

// 计算tag name是否过长
- (NSString *)nameStr:(NSString *)name font:(UIFont *)font byMaxWidth:(CGFloat)maxWidth 
             hasToast:(BOOL)hasToast {

    // 原始字符串的宽度
    CGSize textSize = [name sizeWithAttributes:@{NSFontAttributeName: font}];
    CGFloat realMax = maxWidth;
    if (hasToast) {
        realMax -= 10;
    }
    // 如果字符串的宽度小于最大宽度，直接返回原始字符串
    if (textSize.width <= realMax) {
        return name;
    }

    // 添加省略号，先计算"..."
    NSString *ellipsis = @"...";
    CGSize ellipsisSize = [ellipsis sizeWithAttributes:@{NSFontAttributeName: font}];

    // 创建可变字符串用于逐步截断字符
    NSMutableString *truncatedString = [NSMutableString stringWithString:name];

    // 从末尾逐步删除字符，直到字符串的宽度加上省略号的宽度小于或等于最大宽度
    while (textSize.width + ellipsisSize.width > realMax && truncatedString.length > 0) {
        // 删除最后一个字符
        [truncatedString deleteCharactersInRange:NSMakeRange(truncatedString.length - 1, 1)];
        // 重新计算截断后的字符串宽度
        textSize = [truncatedString sizeWithAttributes:@{NSFontAttributeName: font}];
    }

    // 返回截断后的字符串，并添加省略号
    return [truncatedString stringByAppendingString:ellipsis];
}


- (FNTag *)transformTag:(FNTag *)tagModel {
    FNTag *tag = tagModel;
    if (tag.form == 5) {
        tag.bgcolor = @"#FFFFFF";
        tag.name = tagModel.name;
        tag.borderColor = @"#FFBC8E";
        tag.imageName = @"icon_credit";
        tag.color = @"#FF6C09";
        tag.form = FNTagWordWithBgColorWithBorder;
    }
    tag.fontSize = 11;
    tag.cornerRadius = ceilf(2.f);
    tag.rightMargin = floorf(4.f);
    return tag;
}

/**
 * 缓存tags
 */
- (void)cacheTags:(NSArray<FNTag *> *)tags completion: (void (^)(void))completion {
    dispatch_group_t group = dispatch_group_create();
    [tags enumerateObjectsUsingBlock:^(FNTag * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        dispatch_group_enter(group);
        obj.cornerRadius = 2.f;
        if ([[FNTagImageManager sharedInstance] cacheImageWithFNTag:obj]) {
            dispatch_group_leave(group);
        } else {
            [[FNTagImageManager sharedInstance] imageWithTag:obj success:^(UIImage *image, FNTag *tag, FNTagImageManager *imageManager) {
                dispatch_group_leave(group);
            }];
        }
    }];
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        !completion?:completion();
    });
}



#pragma mark - UICollectionViewDataSource and delegate
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.goodsList.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView 
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {

    UICollectionViewCell *collectionCell = nil;
    WS(weakSelf)
    if (self.isMenu) { 
        //菜谱栏cell
        FNFreshMrFreshMenuList *model = [self.goodsList safeObjectAtIndex:indexPath.item];

        FNFreshMrFreshFeedsFoodsMenuCell *cell =
        [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshFeedsFoodsMenuCellIdentifier
                                                  forIndexPath:indexPath];

        [cell setupWithDataModel:model];
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"145008",
            @"page_id":self.homePageId,
            @"track_type":@"6",
            @"remarks":[[NSString stringWithFormat:@"%ld",self.index + 1] jsonStringForKey:@"tabid"],
        }];

        collectionCell = cell;
    } else {
        FNFreshMrFreshGoodsList *model = [self.goodsList safeObjectAtIndex:indexPath.item];
        
        if (model.flag == 5) {
            // 最佳搭配
            FNFreshMrFreshBestMatchCollectionViewCell *cell = 
            [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshBestMatchCollectionViewCellIdentifier 
                                                      forIndexPath:indexPath];

//            WS(weakSelf)
            [cell setupWithData:model addToCartHandler:^(FNMrFreshGoodsModel *goods, UIImageView *iv) {
                if ([weakSelf.delegate respondsToSelector:
                     @selector(fn_freshMrFreshFeedsGoodsAddToCartWithDataModel:andImageView:andCell:)]) {
                    FNFreshMrFreshGoodsList *goodsList = [FNFreshMrFreshGoodsList new];
                    goodsList.goodsInfo = model.goodsInfo;
                    goodsList.goodsInfo = goods;

                    [weakSelf.delegate fn_freshMrFreshFeedsGoodsAddToCartWithDataModel:goodsList
                                                                          andImageView:iv
                                                                               andCell:cell];
                }
            } updateheightHandler:^(CGFloat height) {
                model.itemHeight = height;
                [UIView performWithoutAnimation:^{
                    [collectionView reloadItemsAtIndexPaths:@[indexPath]];
                }];
            } remarks:[NSString stringWithFormat:@"%ld", self.index + 1]];

            collectionCell = cell;
        } else if (model.flag == 4) {

            // 阅后推荐
            FNFreshMrFreshGuessLikeCollectionViewCell *cell =
            [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshGuessLikeCollectionViewCellIdentifier
                                                      forIndexPath:indexPath];
            [cell setupWithDataModel:model addToCartHandler:^(FNMrFreshGoodsModel *goods, UIImageView *iv) {
                if ([weakSelf.delegate respondsToSelector:
                     @selector(fn_freshMrFreshFeedsGoodsAddToCartWithDataModel:andImageView:andCell:)]) {
                    FNFreshMrFreshGoodsList *goodsList = [FNFreshMrFreshGoodsList new];
                    goodsList.goodsInfo = model.goodsInfo;
                    goodsList.goodsInfo = goods;

                    [weakSelf.delegate fn_freshMrFreshFeedsGoodsAddToCartWithDataModel:goodsList
                                                                          andImageView:iv
                                                                               andCell:cell];
                }
            } remarks:[NSString stringWithFormat:@"%ld", self.index + 1]];

            collectionCell = cell;
            
        } else if (model.flag == 3) {
            // 鲜月票
            FNFreshMrFreshMonthlyTicketCell *cell = 
            [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshMonthlyTicketCellIdentifier
                                                      forIndexPath:indexPath];

            [cell setupWithDataModel:model.monthlyTicket];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"183001",
                @"page_id":self.homePageId,
                @"track_type":@"6",
            }];

            collectionCell = cell;
            
        } else if (model.flag == 2) { 
            // 轮播位
            FNFreshMrFreshFeedsADRotationCell *cell =
            [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshFeedsADRotationCellIdentifier
                                                      forIndexPath:indexPath];
            [cell setupWithDataModel:model handler:^(NSString *linkUrl) {
                [weakSelf openPageWithURLString:linkUrl];
            }];

            collectionCell = cell;
            
        } else if (model.flag == 1) { 
            // 动图位cell
            FNFreshMrFreshFeedsGifPitLocationCell *cell = 
            [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshFeedsGifPitLocationCellIdentifier
                                                      forIndexPath:indexPath];

            [cell setupWithDataModel:model];

            collectionCell = cell;

            WS(weakSelf);
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"153020",
                @"page_id":self.homePageId,
                @"track_type":@"6",
                @"col_position":[NSString stringWithFormat:@"%ld",model.picInfo.picNumber],
                @"remarks":[NSString JSONStringNoFormartForDictionary:@{
                    @"tabid":[NSString stringWithFormat:@"%ld",weakSelf.index + 1],
                    @"url":model.picInfo.pageId.length > 0 ? model.picInfo.pageId : model.picInfo.linkUrl
                }]
            }];
        } else { 
            // 商品位cell
            FNFreshMrFreshFeedsGoodsCollectionViewCell *cell =
            [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshFeedsGoodsCollectionViewCellIdentifier
                                                      forIndexPath:indexPath];

            [cell setupWithDataModel:model 
                           addToCard:^(FNMrFreshGoodsModel *goods,
                                       UIImageView *imageView,
                                       BOOL isMatch) {
                // 加入购物车kFreshRecommendLookAgain
                if ([weakSelf.delegate respondsToSelector:
                     @selector(fn_freshMrFreshFeedsGoodsAddToCartWithDataModel:andImageView:andCell:)])
                {
                    FNFreshMrFreshGoodsList *goodsList = [FNFreshMrFreshGoodsList new];
                    goodsList.goodsInfo = model.goodsInfo;
                    if (isMatch) {
                        goodsList.goodsInfo = goods;
                    } else {
                        // 最佳搭配
                        [weakSelf handlerTheBestMatchWithGoodsModel:model];
                    }
                    [weakSelf.delegate fn_freshMrFreshFeedsGoodsAddToCartWithDataModel:goodsList andImageView:imageView andCell:cell];
                }
                [FNFreshAgent eventWithTrackDataPrameters: @{
                    @"page_col":@"100028",
                    @"page_id":weakSelf.homePageId,
                    @"track_type":@"2",
                    @"col_position":weakSelf.index == 0?@"CNXH":[NSString stringWithFormat:@"%ld",self.index + 1],
                    @"col_pos_content":model.goodsInfo.goodsID ?: @"",
                    @"remarks":weakSelf.index == 0?[NSString JSONStringNoFormartForDictionary:@{
                        @"rank":@(model.goodsInfo.goodsNumber),
                        @"store_show":@(weakSelf.responseModel.storeShow),
                        @"sku_source":@(model.goodsInfo.skuSource)
                    }]:@"",
                    @"abtest":model.goodsInfo.abtest,
                }];
            } remarks:[NSString stringWithFormat:@"%ld",self.index + 1]];

            [self agentProductExposeWithProduct:model.goodsInfo];
            collectionCell = cell;
        }
    }
    
    return collectionCell;
}

- (void)collectionView:(UICollectionView *)collectionView 
       willDisplayCell:(UICollectionViewCell *)cell
    forItemAtIndexPath:(NSIndexPath *)indexPath {

    if (!self.isMenu) {
        [self.visibleGoodsDic setValue:cell forKey:[NSString stringWithFormat:@"%ld",indexPath.item]];
        
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
        [self performSelector:@selector(handleVisibleGoodsDic) withObject:nil afterDelay:0.5];
        
        if (cell.reuseIdentifier == FNFreshMrFreshFeedsADRotationCellIdentifier) {
            [(FNFreshMrFreshFeedsADRotationCell *)cell changeTimerState:YES];
        }
    }
}

- (void)collectionView:(UICollectionView *)collectionView 
  didEndDisplayingCell:(UICollectionViewCell *)cell
    forItemAtIndexPath:(NSIndexPath *)indexPath {

    if (!self.isMenu) {
        [self.visibleGoodsDic removeObjectForKey:[NSString stringWithFormat:@"%ld",indexPath.item]];
        
        if (cell.reuseIdentifier == FNFreshMrFreshFeedsADRotationCellIdentifier) {
            [(FNFreshMrFreshFeedsADRotationCell *)cell changeTimerState:NO];
        }
    }
}

- (CGSize)collectionView:(UICollectionView *)collectionView 
                  layout:(UICollectionViewLayout*)collectionViewLayout
  sizeForItemAtIndexPath:(NSIndexPath *)indexPath {

    CGFloat width = (SCREEN_WIDTH - 33) / 2;
    CGFloat height = ceilf(width * 248 / 171);

    if (self.isMenu) {
        return CGSizeMake(width, height);

    } else {
        FNFreshMrFreshGoodsList *model = [self.goodsList safeObjectAtIndex:indexPath.item];
        if (model.flag == 0) {
            // 商品
            if (model.itemHeight > 0) {
                return CGSizeMake(width, model.itemHeight);
            }
            // 商品位自适应高度
            [self.temCell setupWithDataModel:model addToCard:nil remarks:nil];
            self.temCell.bounds = CGRectMake(0, 0, width, height);
            CGSize autoSize =
            [self.temCell systemLayoutSizeFittingSize:CGSizeMake(width, height)
                        withHorizontalFittingPriority:UILayoutPriorityRequired
                              verticalFittingPriority:UILayoutPriorityFittingSizeLevel];
            height = ceilf(autoSize.height);
            model.itemHeight = height;
            return CGSizeMake(width, height);

        } else if (model.flag == 3) {
            // 鲜月票
            if (model.monthlyTicket.subtitle.length > 0) {
                return CGSizeMake(width, 210 + 79);
            } else {
                return CGSizeMake(width, 215 + 60);
            }

        } else if (model.flag == 4) {
            // 猜你喜欢

            return CGSizeMake(width, 244);

        } else if (model.flag == 5) {
            // 最佳搭配
            if (model.itemHeight > 0) {
                return CGSizeMake(width, model.itemHeight);
            }
            return CGSizeMake(width, 310);
        }
    }
    return CGSizeMake(width, height);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {

    [collectionView deselectItemAtIndexPath:indexPath animated:YES];

    if (self.isMenu) { 
        // 菜谱
        FNFreshMrFreshMenuList *model = [self.goodsList safeObjectAtIndex:indexPath.item];
        FNFreshMenuDetailViewController *vc = [[FNFreshMenuDetailViewController alloc] 
                                               initWithMenuId:model.menuNum];
        [[FNFreshTabBarController shareInstance] pushViewController:vc animated:YES];

        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"145009",
            @"page_id":self.homePageId,
            @"track_type":@"2",
            @"remarks":[[NSString stringWithFormat:@"%ld",self.index + 1] jsonStringForKey:@"tabid"],
        }];
    } else {

        FNFreshMrFreshGoodsList *model = [self.goodsList safeObjectAtIndex:indexPath.item];
        WS(weakSelf);
        if (model.flag == 3) { 
            // 鲜月票
            // router 跳转鲜月票频道页
            [[FNMediator sharedInstance] monthCardInitInLoginedState:^(UIViewController * _Nonnull monthCardVC) {
                [FNFreshTabBarController pushViewController:monthCardVC animated:YES];
            }];

            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"183002",
                @"page_id":self.homePageId,
                @"track_type":@"2",
            }];

        } else if (model.flag == 1) { 
            // 动图位
            [self openPageWithURLString:model.picInfo.linkUrl];

            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"124003",
                @"page_id":self.homePageId,
                @"track_type":@"2",
                @"col_position":[NSString stringWithFormat:@"%ld",model.picInfo.picNumber],
                @"remarks":[NSString JSONStringNoFormartForDictionary:@{
                    @"tabid":[NSString stringWithFormat:@"%ld",weakSelf.index + 1],
                    @"url":model.picInfo.pageId.length > 0 ? model.picInfo.pageId : model.picInfo.linkUrl
                }],
            }];
        } else if (model.flag == 0) { 
            // 商品
            [self requestEnterFeedsWaterFallWithGoods:model];

            NSString *abtest = model.goodsInfo.abtest;
            NSString *col_position = self.index == 0? @"CNXH":[NSString stringWithFormat:@"%ld",self.index + 1];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"178001",
                @"page_id":self.homePageId,
                @"track_type":@"2",
                @"col_position":col_position,
                @"col_pos_content":model.goodsInfo.goodsID ?: @"",
                @"remarks":weakSelf.index == 0?[NSString JSONStringNoFormartForDictionary:@{
                    @"rank":@(model.goodsInfo.goodsNumber),
                    @"store_show":@(weakSelf.responseModel.storeShow),
                    @"sku_source":@(model.goodsInfo.skuSource)
                }]:@"",
                @"abtest":abtest,
            }];
        }
    }
}

- (void)freshFeedsClickGoodsLookDetail:(FNFreshMrFreshGoodsList *)dataModel {

    FNMrFreshGoodsModel *goodsModel = dataModel.goodsInfo;
    NSURL *linkUrl = [NSURL URLWithString:goodsModel.linkUrl];
//    [self openPageWithURLString:goodsModel.linkUrl];
//    [self requestLookAfterRecommendDataWithcateSeq:goodsModel.cpSeq goodsInfo:dataModel];

    NSArray *queryFragments = [linkUrl.query componentsSeparatedByString:@"="];
    NSString *smseq = [queryFragments safeObjectAtIndex:1];
    NSString *queryKey = [queryFragments firstObject];

    if (![linkUrl.host isEqualToString:@"productdetail"] ||
        ![queryKey isEqualToString:kFNSmseqKey] ||
        smseq.length == 0) {

        [self openPageWithURLString:goodsModel.linkUrl];
        return;
    }

    NSString *paramStr = kMerchandiseParmString(goodsModel.goodsID,
                                                goodsModel.saleStoreId,
                                                goodsModel.channelStoreId,
                                                goodsModel.isVoucherGoods,
                                                goodsModel.merchantStoreId,
                                                goodsModel.merchantCode,
                                                goodsModel.merchantType);
    WS(weakSelf)
    UIViewController *detailVC = [[FNMediator sharedInstance] freshMerchandiseDetail_InitializeWithMerchandiseParmString:paramStr serviceId:@"" deallocComplete:^(NSString *cpSeq) {
        [weakSelf requestLookAfterRecommendDataWithcateSeq:cpSeq goodsInfo:dataModel];
    }];
    [[FNFreshTabBarController shareInstance] pushViewController:detailVC animated:YES];

}

- (void)openPageWithURLString:(NSString *)URLString {
    FNFreshUrlRouter *URLRouter = [[FNFreshUrlRouter alloc] init];
    [URLRouter jumpControllerWithRemoteURLString:URLString completion:nil];
}

///商品曝光埋点
- (void)agentProductExposeWithProduct:(FNMrFreshGoodsModel *)goodsModel {

    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":self.index == 0?@"165076":@"165078",
        @"page_id":self.homePageId,
        @"track_type":@"6",
        @"col_position":self.index == 0?@"":[NSString stringWithFormat:@"%ld",self.index + 1],
        @"col_pos_content":goodsModel.goodsID ?: @"",
        @"remarks":self.index == 0?[NSString JSONStringNoFormartForDictionary:@{
            @"rank":@(goodsModel.goodsNumber),
            @"store_show":@(self.responseModel.storeShow),
            @"sku_source":@(goodsModel.skuSource)
        }]:@"",
        @"abtest":goodsModel.abtest,
    }];
}

///请求阅后推荐数据
- (void)requestLookAfterRecommendDataWithcateSeq:(NSString *)cpSeq 
                                       goodsInfo: (FNFreshMrFreshGoodsList *)dataModel {

    // 检查一屏只展示一个阅后推荐
    __block BOOL hasRecommend = NO;
    [self.visibleGoodsDic.allValues enumerateObjectsUsingBlock:
     ^(UICollectionViewCell * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:[FNFreshMrFreshGuessLikeCollectionViewCell class]]) {
            hasRecommend = YES;
            *stop = YES;
        }
    }];
    if (hasRecommend) {
        return;
    }

    FNMrFreshReadAfterRecommendParamModel *parameterModel = 
    [FNMrFreshReadAfterRecommendParamModel new];

    parameterModel.storeCode = [FNFreshUser shareInstance].shopId;
    parameterModel.productCpSeq = cpSeq;
    parameterModel.skuCode = dataModel.goodsInfo.goodsID;

    WS(weakSelf);
    [FNFreshMrFreshService requestReadAfterRecommendWithParameter:parameterModel 
                                                          success:^(id responseObject, BOOL isCache)
     {
        [weakSelf handleReadRecommendResponseObject:responseObject dataItem:dataModel];
    } failure:nil];
}

- (void)handleReadRecommendResponseObject: (FNMrFreshReadAfterRecommendResponseModel *)responseObject 
                                 dataItem: (FNFreshMrFreshGoodsList *)dataItem {
    if (responseObject.goodsList.count < 3) {
        return;
    }
    if (dataItem.isBestMatch) {
        // 同一个商品，只会同时存在阅后推荐/最佳搭配其中之一
        return;
    }

    NSMutableArray<FNMrFreshGoodsModel *> *goodsList = [NSMutableArray array];

    [responseObject.goodsList enumerateObjectsUsingBlock:
     ^(FNFreshProductListMerchandiseModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop)
     {
        // 数据转换FNMrFreshGoodsModel，取前三个
        FNMrFreshGoodsModel *goods = [FNMrFreshGoodsModel new];
        FNMrFreshGoodsShoppingCartInfoModel *goodsToShopCart = [FNMrFreshGoodsShoppingCartInfoModel new];
        goodsToShopCart.kind = obj.kind.stringValue;
        goodsToShopCart.needBuyQty = obj.needBuyQty.stringValue;
        goodsToShopCart.activityId = obj.campaignSeq;
        goodsToShopCart.isLimit = obj.isLimit.stringValue;
        goodsToShopCart.isMultiSpecifition = obj.isMultiSpecifition;

        goods.isHasOption = obj.isPop.integerValue;
        goods.goodsID = obj.productID;
        goods.imgUrl = obj.productPictureURL;
        goods.title = obj.productName;
        goods.subTitle = obj.subTitle;
        goods.price = obj.productRealPrice;
        goods.unit = obj.saleUnit;
        goods.referencePrice = obj.linePrice;
        goods.merchantStoreId = obj.merchantStoreId;
        goods.saleStoreId = obj.saleStoreId;
        goods.isVoucherGoods = obj.isVoucherGoods;
        goods.merchantCode = obj.merchantCode;
        goods.merchantType = obj.merchantType;
        goods.channelStoreId = obj.channelStoreId;
        goods.goodsToShopCart = goodsToShopCart;
        goods.abtest = obj.abtest;
        goods.recommendTag = obj.recommendTag;
        goods.estimateTag = obj.estimateTag;
        [goodsList addObject:goods];
        if (idx == 2) {
            *stop = YES;
        }
    }];

    //曝光埋点
    dataItem.isBestMatch = YES;
    NSInteger currentIndex = [self.goodsList indexOfObject:dataItem];
    FNFreshMrFreshGoodsList *newItem = [FNFreshMrFreshGoodsList new];
    newItem.flag = 4;
    newItem.recommendGoodsArr = [goodsList subarrayWithRange:NSMakeRange(0, 3)];

    /**
     * 位于最后一个的情况下，只能插入下一个，不能+2 否则越界
     */
    NSInteger insertIndex = self.goodsList.count >= currentIndex + 2 ? currentIndex + 2 : currentIndex + 1;
    NSIndexPath *insertIndexPath = [NSIndexPath indexPathForItem:insertIndex inSection:0];

    WS(weakSelf)
    [self.collectionView performBatchUpdates:^{

        [weakSelf.goodsList insertObject:newItem atIndex:insertIndex];
        [weakSelf.collectionView insertItemsAtIndexPaths:@[insertIndexPath]];

    } completion:^(BOOL finished) {
        /**
         * v2.0.8 新增
         * 滑动到中间
         */
        [UIView animateWithDuration:0.5 animations:^{
            [weakSelf.collectionView selectItemAtIndexPath:insertIndexPath
                                                  animated:NO
                                            scrollPosition:UICollectionViewScrollPositionCenteredVertically];
        }];
    }];

}

/// 阅后推荐-曝光埋点
- (void)exposeReadRecommendAgent {
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"131005",
        @"page_id":self.homePageId,
        @"track_type":@"6",
        @"remarks":[[NSString stringWithFormat:@"%ld",self.index + 1] jsonStringForKey:@"tabid"],
    }];
}

/// 最佳搭配处理
- (void)handlerTheBestMatchWithGoodsModel:(FNFreshMrFreshGoodsList *)model {

    if (model.isBestMatch) { 
        // 同一个商品，只会同时存在阅后推荐/最佳搭配其中之一
        return;
    }
    
    // 请求最佳搭配的商品数据，取前三个 from == 5,表示首页购后推荐
    WS(weakSelf)
    [FNFreshRecommendService requestFreshRecommendLookAgainDataWithItemsId:model.goodsInfo.goodsID 
                                                         recommendFromType:5
                                                                   succsee:
     ^(FNFreshRecommendResponseModel *responseObject, BOOL isCache)
     {
        [weakSelf handlerRecommendResponseModel:responseObject dataItem:model];

    } failure:^(id responseObject, NSError *error) {
        
    }];
}
/// 最佳搭配数据处理
- (void)handlerRecommendResponseModel:(FNFreshRecommendResponseModel *)responseObject 
                             dataItem:(FNFreshMrFreshGoodsList *)dataItem {
    FNFreshRecommendListModel *recommendListModel = [responseObject.recommendList firstObject];

    //判断大数据商品数组是否为空
    if (!recommendListModel || recommendListModel.merchandiseList.count < 3) {
        return;
    }

    NSMutableArray<FNMrFreshGoodsModel *> *goodsList = [NSMutableArray array];
    NSMutableArray<NSString *> *goodsIdArr = [[NSMutableArray alloc] initWithCapacity:3];

    [recommendListModel.merchandiseList enumerateObjectsUsingBlock:
     ^(FNFreshProductListMerchandiseModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop)
     {
        // 数据转换FNMrFreshGoodsModel，取前三个
        FNMrFreshGoodsModel *goods = [FNMrFreshGoodsModel new];
        FNMrFreshGoodsShoppingCartInfoModel *goodsToShopCart = [FNMrFreshGoodsShoppingCartInfoModel new];
        goodsToShopCart.kind = obj.kind.stringValue;
        goodsToShopCart.needBuyQty = obj.needBuyQty.stringValue;
        goodsToShopCart.activityId = obj.campaignSeq;
        goodsToShopCart.isLimit = obj.isLimit.stringValue;
        goodsToShopCart.isMultiSpecifition = obj.isMultiSpecifition;

        goods.isHasOption = obj.isPop.integerValue;
        goods.goodsID = obj.productID;
        goods.imgUrl = obj.productPictureURL;
        goods.title = obj.productName;
        goods.subTitle = obj.subTitle;
        goods.price = obj.productRealPrice;
        goods.unit = obj.saleUnit;
        goods.referencePrice = obj.linePrice;
        goods.merchantStoreId = obj.merchantStoreId;
        goods.saleStoreId = obj.saleStoreId;
        goods.isVoucherGoods = obj.isVoucherGoods;
        goods.merchantCode = obj.merchantCode;
        goods.merchantType = obj.merchantType;
        goods.channelStoreId = obj.channelStoreId;
        goods.goodsToShopCart = goodsToShopCart;
        goods.abtest = obj.abtest;
        goods.recommendTag = obj.recommendTag;
        goods.estimateTag = obj.estimateTag;
        [goodsList addObject:goods];
        [goodsIdArr addObject:obj.productID];
        if (idx == 2) {
            *stop = YES;
        }
    }];

    dataItem.isBestMatch = YES;

    // 插入最佳搭配cell
    NSInteger currentIndex = [self.goodsList indexOfObject:dataItem];
    FNFreshMrFreshGoodsList *newItem = [FNFreshMrFreshGoodsList new];
    newItem.flag = 5;
    newItem.recommendGoodsArr = [goodsList subarrayWithRange:NSMakeRange(0, 3)];

    /**
     * 位于最后一个的情况下，只能插入下一个，不能+2 否则越界
     */
    NSInteger insertIndex = self.goodsList.count >= currentIndex + 2 ? currentIndex + 2 : currentIndex + 1;
    NSIndexPath *insertIndexPath = [NSIndexPath indexPathForItem:insertIndex inSection:0];

    WS(weakSelf)
    [self.collectionView performBatchUpdates:^{

        [weakSelf.goodsList insertObject:newItem atIndex:insertIndex];
        [weakSelf.collectionView insertItemsAtIndexPaths:@[insertIndexPath]];


    } completion:^(BOOL finished) {
        /**
         * v2.0.8 新增
         * 滑动到中间
         */
        [UIView animateWithDuration:0.5 animations:^{

            [weakSelf.collectionView selectItemAtIndexPath:insertIndexPath
                                                  animated:NO
                                            scrollPosition:UICollectionViewScrollPositionCenteredVertically];

        }];
    }];
}

#pragma mark UIResponder 负反馈

- (void)feeds_negativeFeedback:(FNFreshMrFreshGoodsList *)model {

    [self.goodsList enumerateObjectsUsingBlock:
     ^(FNFreshMrFreshGoodsList *obj, NSUInteger idx, BOOL * _Nonnull stop)
     {
        obj.isNegativeMask = NO;
        obj.isNegativeGuidance = NO;
        if ([model isEqual:obj]) {
            obj.isNegativeMask = YES;
        }
    }];

    [self.collectionView reloadData];
}

- (void)feeds_lookFamiliar:(FNFreshMrFreshGoodsList *)model {

    FNFreshProductListMerchandiseModel *goods = [FNFreshProductListMerchandiseModel new];
    goods.productName = model.goodsInfo.title;
    goods.productID = model.goodsInfo.goodsID;
    goods.sku_id = model.goodsInfo.goodsID;
    goods.productPictureURL = model.goodsInfo.imgUrl;
    goods.subTitle = model.goodsInfo.subTitle;
    goods.items = model.goodsInfo.items;
    goods.kind = [NSNumber numberWithInteger:model.goodsInfo.goodsToShopCart.kind.integerValue];
    goods.needBuyQty = [NSNumber numberWithInteger:model.goodsInfo.goodsToShopCart.needBuyQty.integerValue];;
    goods.campaignSeq = model.goodsInfo.goodsToShopCart.activityId;
    goods.isLimit = [NSNumber numberWithInteger:model.goodsInfo.goodsToShopCart.isLimit.integerValue];
    goods.isMultiSpecifition = model.goodsInfo.goodsToShopCart.isMultiSpecifition;
    
    goods.isPop = [NSNumber numberWithBool:model.goodsInfo.isHasOption];
    goods.productRealPrice = model.goodsInfo.price;
    goods.saleUnit = model.goodsInfo.unit;
    goods.linePrice = model.goodsInfo.referencePrice;
    goods.merchantStoreId = model.goodsInfo.merchantStoreId;
    goods.saleStoreId = model.goodsInfo.saleStoreId;
    goods.isVoucherGoods = model.goodsInfo.isVoucherGoods;
    goods.merchantCode = model.goodsInfo.merchantCode;
    goods.merchantType = model.goodsInfo.merchantType;
    goods.channelStoreId = model.goodsInfo.channelStoreId;
    goods.abtest = model.goodsInfo.abtest;
    model.isNegativeMask = NO;

    //进入相似商品列表页
    FNFreshShopCartSimilarListController *similarVc = [[FNFreshShopCartSimilarListController alloc] initWithListMerchandiseModel:goods fromType:(FNSimilarFromHomeFeeds)];
    [[FNFreshTabBarController shareInstance] pushViewController:similarVc animated:YES];

}

- (void)feeds_negativeDontLikeThisGoods:(FNFreshMrFreshGoodsList *)model andFeedback:(int)type {
    // 获取要删除的 indexPath
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:[self.goodsList indexOfObject:model] 
                                                 inSection:0];

    // 在 performBatchUpdates 中删除数据源和更新 UICollectionView
    WS(weakSelf)
    [self.collectionView performBatchUpdates:^{
        [weakSelf.goodsList removeObject:model];
        [weakSelf.collectionView deleteItemsAtIndexPaths:@[indexPath]];
    } completion:^(BOOL finished) {
        // 动画完成后的操作
        [weakSelf startProgressText:@"反馈收到，将减少类似推荐"];
    }];
    
    FNFreshNegativeFeedbackParameterModel *paramModel = 
    [[FNFreshNegativeFeedbackParameterModel alloc]init];

    if (type == NotInterested) { // 不感兴趣
        paramModel.skuCode = model.goodsInfo.goodsID;
    } else if (type == DontWantToSeeIt) { // 不想看到它
        paramModel.cpSeq = model.goodsInfo.cpSeq;
    } else { // 对图片感到不适
        paramModel.skuCode = model.goodsInfo.goodsID;
        paramModel.picture = model.goodsInfo.imgUrl;
    }
    [FNFreshRecommendService requestFreshRecommendUserNegativeFeedbackByUserWithParameter:paramModel 
                                                                                  Success:
     ^(id responseObject, BOOL isCache)
     {
        NSLog(@"~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~负反馈成功");
    } failure:^(id responseObject, NSError *error) {
        NSLog(@"~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~负反馈设置失败");
    }];
}


#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    
    if (self.canScroll) {
        CGFloat offsetY = scrollView.contentOffset.y;
        if (offsetY <= 0) {
            [self makePageViewControllerScroll:NO];
            if (self.gestureDelegate && [self.gestureDelegate respondsToSelector:@selector(pageViewControllerLeaveTop:)]) {
                [self.gestureDelegate pageViewControllerLeaveTop:YES];
            }
        }
    } else {
        [self makePageViewControllerScroll:NO];
        if (self.gestureDelegate && [self.gestureDelegate respondsToSelector:@selector(pageViewControllerLeaveTop:)]) {
            [self.gestureDelegate pageViewControllerLeaveTop:NO];
        }
    }
}

- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset {
    // 滑动倒数第二屏开始加载
    BOOL isLastSecondSreen = (scrollView.contentSize.height - 2 * scrollView.bounds.size.height) < targetContentOffset->y;

    if (velocity.y > 0 && isLastSecondSreen &&
        self.collectionView.mj_footer.state != MJRefreshStateNoMoreData &&
        !self.collectionView.mj_footer.isRefreshing)
    {
        [self.collectionView.mj_footer beginRefreshing];
    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (!decelerate && self.gestureDelegate && [self.gestureDelegate respondsToSelector:@selector(pageViewControllerDidEndScroll:)]) {
        [self.gestureDelegate pageViewControllerDidEndScroll:YES];
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if (self.gestureDelegate && [self.gestureDelegate respondsToSelector:@selector(pageViewControllerDidEndScroll:)]) {
        [self.gestureDelegate pageViewControllerDidEndScroll:YES];
    }
}


#pragma mark - public methods
- (void)makePageViewControllerScroll:(BOOL)canScroll {
    self.canScroll = canScroll;
    if (!canScroll) {
        self.collectionView.contentOffset = CGPointZero;
    }
}

- (void)makePageViewControllerScrollToTop {
    [self.collectionView setContentOffset:CGPointZero];
}

- (void)makeScrollTargetOffset:(CGPoint)contentOffset {
    
    CGPoint roundPoint = CGPointMake(0, contentOffset.y + arc4random()%400);
    if (self.collectionView.contentSize.height > roundPoint.y) {
        [self.collectionView setContentOffset:roundPoint animated:YES];
        if (self.gestureDelegate && [self.gestureDelegate respondsToSelector:@selector(pageViewControllerDidEndScroll:)]) {
            [self.gestureDelegate pageViewControllerDidEndScroll:YES];
        }
    }
    
}

- (void)setNegativeFeedbackDismiss {
    if (self.index != 0 || self.isMenu) {
        return;
    }
    [self.goodsList enumerateObjectsUsingBlock:
     ^(FNFreshMrFreshGoodsList *obj, NSUInteger idx, BOOL * _Nonnull stop)
     {
        obj.isNegativeMask = NO;
    }];
    
    for (UICollectionViewCell *cell in self.visibleGoodsDic.allValues) {
        if ([cell isKindOfClass:[FNFreshMrFreshFeedsGoodsCollectionViewCell class]]) {
            [(FNFreshMrFreshFeedsGoodsCollectionViewCell *)cell setFeedbackHidden];
        }
    }
}

#pragma mark - private methods
- (void)setup {
    self.collectionView.backgroundColor = [UIColor fn_colorWithColorKey:kFNViewBgKey];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshFeedsGoodsCollectionViewCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshMrFreshFeedsGoodsCollectionViewCellIdentifier];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshFeedsFoodsMenuCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshMrFreshFeedsFoodsMenuCellIdentifier];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshFeedsGifPitLocationCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshMrFreshFeedsGifPitLocationCellIdentifier];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshMonthlyTicketCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshMrFreshMonthlyTicketCellIdentifier];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshFeedsADRotationCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshMrFreshFeedsADRotationCellIdentifier];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshGuessLikeCollectionViewCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshMrFreshGuessLikeCollectionViewCellIdentifier];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshBestMatchCollectionViewCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshMrFreshBestMatchCollectionViewCellIdentifier];
    
    self.collectionView.alwaysBounceVertical = YES;
    self.collectionView.showsVerticalScrollIndicator = NO;
    
    FNFreshNormalRefreshHeader *header = [FNFreshNormalRefreshHeader headerWithRefreshingTarget:self refreshingAction:@selector(reqestFeedsGoods)];
    
    [header beginRefreshing];
    
    FNFreshHomeFeedsMJFooter *footer = [FNFreshHomeFeedsMJFooter footerWithRefreshingTarget:self refreshingAction:@selector(reqestFeedsGoods)];
    [footer setTitle:@"已经到底啦" forState:MJRefreshStateNoMoreData];
    
    self.collectionView.mj_header = header;
    self.collectionView.mj_footer = footer;
}

- (NSMutableArray *)goodsList {
    if (!_goodsList) {
        _goodsList = [NSMutableArray array];
    }
    return _goodsList;
}

- (NSMutableDictionary<NSString *,UICollectionViewCell *> *)visibleGoodsDic {
    if (!_visibleGoodsDic) {
        _visibleGoodsDic = [NSMutableDictionary dictionary];
    }
    return _visibleGoodsDic;
}

//- (void)postAgent:(NSArray *)goodsList {
//    NSMutableArray *goodsIdArr = [NSMutableArray array];
//    for (FNFreshMrFreshGoodsList *model in goodsList) {
//        if (!model.flag) {
//            [goodsIdArr addObject:model.goodsInfo.goodsID];
//        }
//    }
//    NSString *goodsIds = [goodsIdArr componentsJoinedByString:@","];
//    NSString *position = self.sort?@"1":@"2";
//    NSString *abtest = self.abtest?:@"";
//    [FNFreshAgent eventWithTrackDataPrameters: @{
//        @"page_col":@"127079",
//        @"page_id":self.homePageId,
//        @"track_type":@"6",
//        @"col_position":position,
//        @"col_pos_content":goodsIds,
//        @"abtest":self.sort?abtest:@"",
//    }];
//}

/**
 * 为了找到父类视图 - FNFreshMrFreshFeedsCollectionViewCell
 * 可以调FNFreshMrFreshFeedsCollectionViewCell的-selectIndex:来实现切换下个tab的需求
 */
- (__kindof UIView *)findSuperviewOfClass:(Class)cls {
    UIView *currentView = self.view;
    while (currentView.superview) {
        if ([currentView.superview isKindOfClass:cls]) {
            return currentView.superview;
        }
        currentView = currentView.superview;
    }
    return nil;
}

@end
