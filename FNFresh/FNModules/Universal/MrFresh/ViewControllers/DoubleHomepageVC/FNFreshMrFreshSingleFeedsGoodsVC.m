//
//  FNFreshMrFreshSingleFeedsGoodsVC.m
//  FNFresh
//
//  Created by wang<PERSON> on 2024/5/30.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshSingleFeedsGoodsVC.h"
#import <UIViewController+FNNavigationBarHidden.h>
#import "UIViewController+FNFreshBundleHandler.h"
#import "FNFreshMrFreshFeedsGoodsCardCollectionCell.h"
#import "FNFreshMrFreshFeedsGuidanceViewController.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "FNFreshUtils.h"
#import "FNFreshMrFreshFeedsEnterParameterModel.h"
#import "FNFreshMrFreshFeedsEnterResponseModel.h"
#import "FNFreshMrFreshService.h"
#import "FNFreshHomeFeedsMJFooter.h"
#import "FNMediator+FNFreshMerchandiseDetail.h"
#import "FNFreshMerchandiseAddToCartItemModel.h"
#import "FNFreshTabBarController.h"
#import "UIResponder+FNHomeEvent.h"
#import "UIImage+FNOptimize.h"
#import "FNFreshMerchandiseAddToCartHandler.h"
#import "UIView+AddShopcartAnimation.h"
#import "FNVideoPlayerConfig.h"

static NSString * const FNFreshMrFreshFeedsGoodsCardCollectionCellIdentifier =
@"FNFreshMrFreshFeedsGoodsCardCollectionCell";

@interface FNFreshMrFreshSingleFeedsGoodsVC ()<UICollectionViewDataSource, UICollectionViewDelegateFlowLayout, FNFreshMerchandiseAddToCartHandlerDelegate>

@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UIImageView *bgBlurImgView;
@property (weak, nonatomic) IBOutlet UIImageView *scrollToTopIV;
@property (weak, nonatomic) IBOutlet UILabel *topMaskView;

@property (copy, nonatomic) NSString *goodsId;
@property (copy, nonatomic) NSMutableArray<FNFreshMrFreshFeedsWallGoodsInfo *> *goodsList;
@property (strong, nonatomic) FNFreshMrFreshFeedsGoodsCardCollectionCell *tempCell;
@property (strong, nonatomic) FNFreshMrFreshFeedsGoodsCardCollectionCell *currentCell;
@property (assign, nonatomic) BOOL isFirst;
@property (assign, nonatomic) BOOL hasNextPage;
@property (assign, nonatomic) NSInteger tabType;
@property (assign, nonatomic) NSInteger currentPage;

// 加入购物车动画--- 记录添加的商品图片 、frame
@property (strong, nonatomic) UIImage *addToCardImgView;
@property (assign, nonatomic) CGRect addToCardImgRect;
@property (strong, nonatomic) FNFreshMerchandiseAddToCartHandler *addToCartHandler;

@property (copy, nonatomic) void (^deallocComplete)(void);

@end

@implementation FNFreshMrFreshSingleFeedsGoodsVC

- (instancetype)initWithGoodsId:(NSString *)goodsId
                   andFirstPage:(NSArray *)goodsList
                    hasNextPage:(BOOL)hasNextPage
                        tabType:(NSInteger)tabType
                deallocComplete:(void(^)(void))deallocComplete {
    if (self = [super initWithFreshBundle]) {
        self.goodsId = goodsId;
        self.deallocComplete = deallocComplete;
        self.tabType = tabType;
        [self.goodsList addObjectsFromArray:goodsList];
        self.hasNextPage = hasNextPage;
        self.isFirst = YES;
    }
    return self;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];

    [[NSNotificationCenter defaultCenter] postNotificationName:kFNProductDetailSwitchPlayerNotificationKey object:nil userInfo:@{kFNVideoPlayerPlayStateKey: @(FNVideoPlayerPlayStateStop)}];
    
    // 判断是否是因为返回操作导致的页面消失
    if (self.isMovingFromParentViewController || self.isBeingDismissed) {
        NSDictionary *parameter = @{
            @"page_col":@"208023",
            @"page_id":@"321",
            @"track_type":@"2",
            @"col_pos_content":self.goodsId
        };
        [FNFreshAgent eventWithTrackDataPrameters:parameter];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];

    [self setupUI];
    // Do any additional setup after loading the view.

    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"197003",
        @"page_id":@"321",
        @"track_type":@"1",
        @"remarks":[NSString JSONStringNoFormartForDictionary:@{
            @"fromsku":self.goodsId
        }]
    }];
}

- (void)showNewGuideVC {
    if (![FNFreshUtils shareInstance].hasShowFeedsGuidance) {

        FNFreshMrFreshFeedsGuidanceViewController *viewController = [FNFreshMrFreshFeedsGuidanceViewController initWithMessage:nil andHandler:^{
            [FNFreshUtils shareInstance].hasShowFeedsGuidance = YES;
        }];
        CGSize size = CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT);
        [self fn_presentViewController:viewController customAnimateType:FNFreshMrFreshCustomAnimateTypeNone viewSize:size duration:0 alpha:0 handle:nil];
    }
}

- (void)setupUI {
    self.fnPreferNavigationBarHidden = YES;
    [self.collectionView setContentInsetAdjustmentBehavior:UIScrollViewContentInsetAdjustmentNever];
    self.collectionView.decelerationRate = UIScrollViewDecelerationRateFast;

    //    [self.topMaskView createGradientWithColors:@[[UIColor colorWithHex:0x000000 alpha:1],
    //                                                 [UIColor colorWithHex:0x000000 alpha:1]]
    //                                     direction:UIViewLinearGradientDirectionVertical];
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.startPoint = CGPointMake(0.5, 0);
    gradientLayer.endPoint = CGPointMake(0.5, 1);
    gradientLayer.colors = @[(id)[UIColor colorWithHex:0x000000 alpha:1].CGColor,
                             (id)[UIColor colorWithHex:0x000000 alpha:0].CGColor];
    gradientLayer.frame = CGRectMake(0, 0, SCREEN_WIDTH, 42);
    [self.topMaskView.layer addSublayer:gradientLayer];


    // Register cell classes
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshMrFreshFeedsGoodsCardCollectionCellIdentifier bundle:nil]
          forCellWithReuseIdentifier:FNFreshMrFreshFeedsGoodsCardCollectionCellIdentifier];

    FNFreshHomeFeedsMJFooter *footer = [FNFreshHomeFeedsMJFooter footerWithRefreshingTarget:self refreshingAction:@selector(delayRequest)];
    footer.stateLabel.textColor = [UIColor whiteColor];
    [footer setTitle:@"已经到底啦" forState:MJRefreshStateNoMoreData];
    self.collectionView.mj_footer = footer;

    //    [self requestData];
    self.currentPage = 1;
    [self.collectionView reloadData];
    if (!self.hasNextPage) {
        [self.collectionView.mj_footer endRefreshingWithNoMoreData];
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 新手引导
        [self showNewGuideVC];
    });
}

- (void)responseHomeRecycleBannerImg:(NSURL *)imgUrl andBgColor:(NSString *)color {
    //    self.bgBlurImgView
    WS(weakSelf)
    [[SDWebImageManager sharedManager] loadImageWithURL:imgUrl options:SDWebImageHighPriority progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {

        if (image) {
            // 1、缩放bg图片到屏幕宽度
            CGFloat scaleHeight = SCREEN_WIDTH * image.size.height / image.size.width;
            UIImage *resizeImg = [image fn_imageByResizeToSize:CGSizeMake(SCREEN_WIDTH, scaleHeight)];
            CATransition *transition = [CATransition animation];
            transition.duration = 0.5;
            transition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
            transition.type = kCATransitionFade;
            [weakSelf.bgBlurImgView.layer addAnimation:transition forKey:@"a"];
            weakSelf.bgBlurImgView.image = [weakSelf coreBlurImage:resizeImg withBlurRadius:40.f];
        }
    }];
}

- (void)delayRequest {
    // do nothing
}

- (void)requestData {
    [self startProgress];
    self.currentPage++;
    FNFreshMrFreshFeedsEnterParameterModel *para = [[FNFreshMrFreshFeedsEnterParameterModel alloc] init];
    para.store_id = [FNFreshUser shareInstance].shopId;
    para.goods_no = self.goodsId;
    para.page_index = self.currentPage;
    para.tab_type = self.tabType;
    WS(weakSelf)
    [FNFreshMrFreshService requestWhetherToEnterFeedsWithParameter:para success:^(FNFreshMrFreshFeedsEnterResponseModel *responseObject, BOOL isCache) {

        [weakSelf stopProgress];
        // reload ui
        NSInteger startIndex = weakSelf.goodsList.count;
        [weakSelf.goodsList addObjectsFromArray:responseObject.goodsList];
        NSInteger endIndex = weakSelf.goodsList.count;
        NSMutableArray *indexPaths = [NSMutableArray array];
        for(NSInteger i = startIndex; i < endIndex; i++) {
            [indexPaths addObject:[NSIndexPath indexPathForRow:i inSection:0]];
        }
        [weakSelf.collectionView performBatchUpdates:^{
            [weakSelf.collectionView insertItemsAtIndexPaths:indexPaths];
        } completion:nil];

        // mj_footer
        weakSelf.hasNextPage = responseObject.hasNextPage;
        if (responseObject.hasNextPage) {
            [weakSelf.collectionView.mj_footer endRefreshing];
        } else {
            [weakSelf.collectionView.mj_footer endRefreshingWithNoMoreData];
        }

    } failure:^(FNFreshMrFreshFeedsEnterResponseModel *responseObject, NSError *error) {
        // 暂无数据
        [weakSelf stopProgress];

        [weakSelf startProgressText:responseObject.errorDesc];

    }];
}

/**
 图片高斯模糊
 */
- (UIImage *)coreBlurImage:(UIImage *)image withBlurRadius:(CGFloat)blur
{
    CIImage *ciImage = [CIImage imageWithCGImage:image.CGImage];
    CIFilter *filter = [CIFilter filterWithName:@"CIGaussianBlur"];
    [filter setValue:ciImage forKey:kCIInputImageKey];
    //设置高斯模糊强度 0 - 100
    [filter setValue:@(blur) forKey:kCIInputRadiusKey];
    CIImage *result = [filter valueForKey:kCIOutputImageKey];
    CIContext *context = [CIContext contextWithOptions:nil];
    CGImageRef outImage = [context createCGImage: result fromRect:ciImage.extent];
    UIImage * blurImage = [UIImage imageWithCGImage:outImage];
    return blurImage;
}


#pragma mark click actions

- (IBAction)backClick:(id)sender {
    [self.navigationController popViewControllerAnimated:YES];
}

- (IBAction)scrowToTop:(id)sender {
    [self.collectionView setContentOffset:CGPointZero animated:YES];
    self.currentCell = [self.collectionView cellForItemAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0]];
    self.scrollToTopIV.hidden = YES;
}

#pragma mark <UICollectionViewDataSource>

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.goodsList.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView
                  cellForItemAtIndexPath:(NSIndexPath *)indexPath {

    FNFreshMrFreshFeedsGoodsCardCollectionCell *cell =
    [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshMrFreshFeedsGoodsCardCollectionCellIdentifier
                                              forIndexPath:indexPath];

    // Configure the cell
    FNFreshMrFreshFeedsWallGoodsInfo *item = [self.goodsList safeObjectAtIndex:indexPath.item];
    WS(weakSelf)
    [cell setupDataModel:item handler:^(UIImageView *iv, FNFreshMrFreshFeedsWallGoodsInfo *goods, BOOL isLeft) {
        if (iv && goods) {
            // 加购
            [weakSelf setShopCartWithImageView:iv andGoodsModel:goods source:nil];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"100028",
                @"page_id":@"321",
                @"track_type":@"2",
                @"col_pos_content":item.goodsId,
                @"remarks":[NSString JSONStringNoFormartForDictionary:@{
                    @"fromsku":weakSelf.goodsId,
                    @"sku_source":@(item.skuSource),
                    @"sku_rank":@(indexPath.item + 1)
                }]
            }];
        } else {
            // 进商详
            [weakSelf freshFeedsClickGoodsLookDetail:goods rank:indexPath.item + 1 isLeft:isLeft];
        }
    }];
    cell.vc = self;
    if (indexPath.item == 0 && self.isFirst) {
        self.isFirst = NO;
        self.currentCell = cell;
        self.currentCell.isCurrent = YES;
    }
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"197004",
        @"page_id":@"321",
        @"track_type":@"6",
        @"col_pos_content":item.goodsId,
        @"remarks":[NSString JSONStringNoFormartForDictionary:@{
            @"fromsku":weakSelf.goodsId,
            @"sku_source":@(item.skuSource),
            @"sku_rank":@(indexPath.item + 1)
        }]
    }];
    if (indexPath.item == self.goodsList.count - 1) {
        // 已经到底啦
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"197005",
            @"page_id":@"321",
            @"track_type":@"6",
            @"remarks":[NSString JSONStringNoFormartForDictionary:@{
                @"fromsku":weakSelf.goodsId,
            }]
        }];
    }

    return cell;
}

-(void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];

    FNFreshMrFreshFeedsWallGoodsInfo *item = [self.goodsList safeObjectAtIndex:indexPath.item];
    [self freshFeedsClickGoodsLookDetail:item rank:indexPath.item + 1 isLeft:NO];
}


- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = SCREEN_WIDTH - 24;
    CGFloat height = 600;

    // 商品位自适应高度
    FNFreshMrFreshFeedsWallGoodsInfo *item = [self.goodsList safeObjectAtIndex:indexPath.item];
    if (item.itemHeight > 0) {
        return CGSizeMake(width, item.itemHeight);
    }
    [self.tempCell setupDataModel:item handler:nil];
    self.tempCell.bounds = CGRectMake(0, 0, width, height);
    CGSize autoSize =
    [self.tempCell systemLayoutSizeFittingSize:CGSizeMake(width, height)
                 withHorizontalFittingPriority:UILayoutPriorityRequired
                       verticalFittingPriority:UILayoutPriorityFittingSizeLevel];
    height = ceilf(autoSize.height);
    item.itemHeight = height;

    return CGSizeMake(width, height);
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {

}

- (void)collectionView:(UICollectionView *)collectionView didEndDisplayingCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {

}

/**
 * 进商详
 * isLeft 是否是通过左滑
 */
- (void)freshFeedsClickGoodsLookDetail:(FNFreshMrFreshFeedsWallGoodsInfo *)goodsModel
                                  rank:(NSInteger)rank
                                isLeft:(BOOL)isLeft {

    NSString *paramStr = kMerchandiseParmString(goodsModel.goodsId,
                                                goodsModel.saleStoreId,
                                                goodsModel.channelStoreId,
                                                goodsModel.isVoucherGoods,
                                                goodsModel.merchantStoreId,
                                                goodsModel.merchantCode,
                                                goodsModel.merchantType);
    // 跳转商详vc
    UIViewController *detailVC = [[FNMediator sharedInstance] freshMerchandiseDetail_InitializeWithMerchandiseParmString:paramStr serviceId:@"" deallocComplete:^(NSString *cpSeq) {
    }];

    // 修复：确保在主线程中执行跳转，并添加延迟以避免弹窗状态冲突
    dispatch_async(dispatch_get_main_queue(), ^{
        // 检查navigationController是否存在
        if ([FNFreshTabBarController shareInstance].navigationController) {
            [[FNFreshTabBarController shareInstance] pushViewController:detailVC animated:YES];
        } else {
            // 如果navigationController暂时不可用，稍后重试
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[FNFreshTabBarController shareInstance] pushViewController:detailVC animated:YES];
            });
        }
    });
    WS(weakSelf)
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"178001",
        @"page_id":@"321",
        @"track_type":@"2",
        @"col_position":isLeft?@"2":@"1",
        @"col_pos_content":goodsModel.goodsId,
        @"remarks":[NSString JSONStringNoFormartForDictionary:@{
            @"fromsku":weakSelf.goodsId,
            @"sku_source":@(goodsModel.skuSource),
            @"sku_rank":@(rank)
        }]
    }];
}

#pragma mark <UIScrollViewDelegate>
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView
                     withVelocity:(CGPoint)velocity
              targetContentOffset:(inout CGPoint *)targetContentOffset {
    NSIndexPath *lastIndexPath = [self.collectionView indexPathForCell:self.currentCell];
    if (self.currentCell) {
        CGFloat cellHeight = self.currentCell.frame.size.height;
        CGFloat threshold = cellHeight / 3.0; // 手势滚动阈值
        CGFloat distance = targetContentOffset->y - self.currentCell.frame.origin.y + 45;

        NSIndexPath *targetIndexPath = nil;
        if (distance > threshold && lastIndexPath.item < self.goodsList.count - 1) {
            targetIndexPath = [NSIndexPath indexPathForItem:lastIndexPath.item + 1 inSection:0];
        } else if (distance < -threshold && lastIndexPath.item > 0) {
            targetIndexPath = [NSIndexPath indexPathForItem:lastIndexPath.item - 1 inSection:0];
        } else {
            targetIndexPath = lastIndexPath;
        }

        UICollectionViewCell *targetCell = [self.collectionView cellForItemAtIndexPath:targetIndexPath];
        if (!targetCell) {
            [self.collectionView scrollToItemAtIndexPath:targetIndexPath atScrollPosition:UICollectionViewScrollPositionTop animated:NO];
            targetCell = [self.collectionView cellForItemAtIndexPath:targetIndexPath];
        }
        CGFloat targetOffsetY = targetCell.frame.origin.y - 45;
        targetContentOffset->y = targetOffsetY;
        if (self.currentCell == targetCell) {
            return;
        }
        if (targetIndexPath.item == self.goodsList.count - 1) {
            CGFloat bottom = SCREEN_HEIGHT - (scrollView.contentSize.height + scrollView.contentInset.bottom - targetOffsetY);
            scrollView.mj_insetB += bottom;
        }
        self.currentCell.isCurrent = NO;
        self.currentCell = (FNFreshMrFreshFeedsGoodsCardCollectionCell *)targetCell;
    }

    // 判断指定按钮的显隐藏
    NSInteger currentCellIndex = [self.collectionView indexPathForCell:self.currentCell].item;
    self.scrollToTopIV.hidden = currentCellIndex < 3;

    // 提前请求数据
    if (currentCellIndex >= self.goodsList.count - 2 && self.hasNextPage) {
        // 条件滑动到倒数第二个cell的时候加载更多
        [self requestData];
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    self.currentCell.isCurrent = YES;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (scrollView.contentOffset.y == 0 && self.goodsList.count > 0) {
        self.scrollToTopIV.hidden = YES;
        self.currentCell = [self.collectionView cellForItemAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0]];
        self.currentCell.isCurrent = YES;
    }
}

- (BOOL)scrollViewShouldScrollToTop:(UIScrollView *)scrollView {
    return false;
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView {
    if (scrollView.contentOffset.y == 0) {

    }
}

#pragma mark -- setter & getter
- (NSMutableArray<FNFreshMrFreshFeedsWallGoodsInfo *> *)goodsList {
    if (!_goodsList) {
        _goodsList = [NSMutableArray array];
    }
    return _goodsList;
}

- (void)setCurrentCell:(FNFreshMrFreshFeedsGoodsCardCollectionCell *)currentCell {
    if (_currentCell != currentCell) {
        [_currentCell pauseVideo];
    }
    _currentCell = currentCell;
    NSInteger currentIndex = [self.collectionView indexPathForCell:currentCell].item;
    FNFreshMrFreshFeedsWallGoodsInfo *currentGoodsInfo = [self.goodsList safeObjectAtIndex:currentIndex];
    if (currentGoodsInfo.videoUrl.length) {

        [_currentCell resumeVideo:currentGoodsInfo];
    }
}

// 获取tempCell
- (FNFreshMrFreshFeedsGoodsCardCollectionCell *)tempCell {
    if (!_tempCell) {
        _tempCell = [[UINib nibWithNibName:@"FNFreshMrFreshFeedsGoodsCardCollectionCell" bundle:nil] instantiateWithOwner:self options:nil].lastObject;
    }
    return _tempCell;
}

- (FNFreshMerchandiseAddToCartHandler *)addToCartHandler {
    if (!_addToCartHandler) {
        _addToCartHandler = [[FNFreshMerchandiseAddToCartHandler alloc] initWithDelegate:self];
    }
    return _addToCartHandler;
}

- (void)dealloc {
    if (self.deallocComplete) {
        self.deallocComplete();
    }
}

#pragma mark -- 加购
- (void)setShopCartWithImageView:(UIImageView *)imgView andGoodsModel:(FNFreshMrFreshFeedsWallGoodsInfo *)goodsModel source:(NSString *)source {
    self.addToCardImgView = imgView.image;
    self.addToCardImgRect = [[UIApplication sharedApplication].keyWindow convertRect:imgView.frame fromView:imgView.superview];
    [self addToShopCartWithGoodsModel:goodsModel source:source];
}

- (void)addToShopCartWithGoodsModel:(FNFreshMrFreshFeedsWallGoodsInfo *)goodsModel source:(NSString *)source {

    void(^block)(void) = ^{

        FNFreshMerchandiseAddToCartItemModel *itemModel = [[FNFreshMerchandiseAddToCartItemModel alloc] init];
        itemModel.merchandiseId = goodsModel.goodsId;
        itemModel.qty = goodsModel.needBuyQty;
        itemModel.kind = goodsModel.kind;
        itemModel.campaignSeq = goodsModel.campaign_seq;
        itemModel.source = source;
        itemModel.isVoucherGoods = goodsModel.isVoucherGoods;

        FNShopCartMerchantInfo *merchantStoreInfo = [[FNShopCartMerchantInfo alloc] init];
        merchantStoreInfo.channelStoreId = goodsModel.channelStoreId;
        merchantStoreInfo.merchantCode = goodsModel.merchantCode;
        merchantStoreInfo.merchantType = goodsModel.merchantType;
        merchantStoreInfo.saleStoreId = goodsModel.saleStoreId;
        merchantStoreInfo.merchantStoreId = goodsModel.merchantStoreId;

        itemModel.merchantStoreInfo = merchantStoreInfo;

        FNMerchandiseAddToCartExtraModel *extraModel = [FNMerchandiseAddToCartExtraModel new];
        extraModel.isPop = goodsModel.isPop;
        extraModel.isMultiSpec = [goodsModel.isMultiSpecifition isEqualToString:@"1"];
        extraModel.isPairItem = goodsModel.isPairItem.boolValue;
        extraModel.shield = goodsModel.shield;
        [self.addToCartHandler addToShopcartWithParameterItemArray:(@[itemModel]) extra:extraModel];
    };
    if (goodsModel.isLimited.integerValue && ![FNFreshUser shareInstance].isLogin) {
        [FNFreshTarget_LoginModule_Helper login_initWithComplete:block];
    } else {
        block();
    }
}

- (void)addToShopCardAnmation:(FNFreshMerchandiseAddToCartResultModel *)resultModel isToast:(BOOL)isToast {
    __weak typeof (self)weakSelf = self;
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    UIImageView *animateImg = [[UIImageView alloc] initWithFrame:self.addToCardImgRect];
    [animateImg setImage:self.addToCardImgView?:[UIImage imageNamed:@"pic_placeholder"]];
    animateImg.layer.cornerRadius = 55;
    animateImg.layer.masksToBounds = YES;
    [keyWindow addSubview:animateImg];

    // 动画
    [UIView animateWithDuration:0.3 animations:^{
        [animateImg setFrame:CGRectMake(animateImg.frame.origin.x + 30, animateImg.frame.origin.y -150, 110, 110)];
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.5 animations:^{
            CGAffineTransform  rotate = CGAffineTransformMakeRotation(M_PI/2);
            [animateImg setTransform:rotate];

            [animateImg setFrame:CGRectMake(0,0,40, 40)];
            animateImg.center = self.currentCell.shoppingCartImgView.center;

        } completion:^(BOOL finished) {
            [animateImg removeFromSuperview];
            weakSelf.addToCardImgView = nil;
            weakSelf.addToCardImgRect = CGRectZero;
            [weakSelf addShopCarNum:resultModel isToast:isToast];
            //购物车TabIcon 缩放动画
            [self.currentCell.shoppingCartImgView iconTransformAnimationWithScale:1.2
                                                                         duration:0.2
                                                                        completed:nil];
        }];
    }];
}
- (void)addShopCarNum:(FNFreshMerchandiseAddToCartResultModel *)resultModel isToast:(BOOL)isToast {
    if (isToast) {
        [self startShoppingCartProgressText:resultModel.addCartPromptMsg];
    }

}

// 开始请求规格信息
- (void)addToCartHandlerDidStartRequestSpecification:(FNFreshMerchandiseAddToCartHandler *)handler
{
    [self startProgress];

}
// 请求规格信息完成
- (void)addToCartHandlerDidCompleteRequestSpecification:(FNFreshMerchandiseAddToCartHandler *)handler error:(NSError *)error
{
    [self stopProgress];
    if (error) {
        NSString *errStr = [error.userInfo safeObjectForKey:NSLocalizedDescriptionKey];

        [self startProgressText:errStr];
    }
}

- (void)addToCartHandlerDidStartAddToShopCart:(FNFreshMerchandiseAddToCartHandler *)handler {

    [self startProgress];
}

- (void)addToCartHandlerDidCompleteAddToShopCart:(FNFreshMerchandiseAddToCartHandler *)handler resultModel:(FNFreshMerchandiseAddToCartResultModel *)resultModel {

    [self stopProgress];

    switch (resultModel.resultType) {
        case FNMerchandiseAddToCartResultTypeSuccess:
        {
            [self addToShopCardAnmation:resultModel isToast:NO];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
            if (self.currentCell && [self.currentCell respondsToSelector:@selector(updateBadge:)]) {
                [self.currentCell performSelector:@selector(updateBadge:) withObject:[NSString stringWithFormat:@"%ld",resultModel.goodsCount]];
#pragma clang diagnostic pop
            }
        }
            break;
        case FNMerchandiseAddToCartResultTypeFailure:
        {
            [self startProgressText:resultModel.addCartPromptMsg];
        }
            break;
        case FNMerchandiseAddToCartResultTypeLimitPurchase:
        {
            [self startProgressText:resultModel.addCartPromptMsg delay:1.5];
            WS(weakSelf);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)),
                           dispatch_get_main_queue(), ^{
                [weakSelf addToShopCardAnmation:resultModel
                                        isToast:NO];
            });
        }
            break;
        default:
            break;
    }
}


@end
