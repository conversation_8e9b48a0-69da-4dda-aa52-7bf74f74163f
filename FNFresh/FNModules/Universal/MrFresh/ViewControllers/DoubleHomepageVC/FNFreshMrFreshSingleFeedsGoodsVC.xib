<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FNFreshMrFreshSingleFeedsGoodsVC">
            <connections>
                <outlet property="bgBlurImgView" destination="fb4-02-wJ5" id="PBE-dK-Kb3"/>
                <outlet property="collectionView" destination="yhF-o0-4IY" id="igJ-9G-eWZ"/>
                <outlet property="scrollToTopIV" destination="Cgj-Fl-7yR" id="2li-8R-B5n"/>
                <outlet property="topMaskView" destination="FhK-fN-dE3" id="PnW-gJ-cz8"/>
                <outlet property="view" destination="dUW-uW-oeX" id="K3H-OB-bGd"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="dUW-uW-oeX">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="fb4-02-wJ5">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                </imageView>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="yhF-o0-4IY">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" white="0.0" alpha="0.69539114932885904" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="12" minimumInteritemSpacing="0.0" id="PA0-6f-CO6">
                        <size key="itemSize" width="128" height="128"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="45" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="G5t-xD-3WO"/>
                        <outlet property="delegate" destination="-1" id="wak-63-e8C"/>
                    </connections>
                </collectionView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DL9-p1-0kk">
                    <rect key="frame" x="24" y="57" width="30" height="30"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_return_listpage_n" translatesAutoresizingMaskIntoConstraints="NO" id="Zr4-2w-DUW">
                            <rect key="frame" x="9.6666666666666643" y="5.3333333333333357" width="11" height="19.666666666666668"/>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.30348154362416108" colorSpace="custom" customColorSpace="sRGB"/>
                    <gestureRecognizers/>
                    <constraints>
                        <constraint firstItem="Zr4-2w-DUW" firstAttribute="centerX" secondItem="DL9-p1-0kk" secondAttribute="centerX" id="7MY-9x-dN9"/>
                        <constraint firstAttribute="width" constant="30" id="ViJ-W5-e2b"/>
                        <constraint firstAttribute="height" constant="30" id="Xoq-DP-bXn"/>
                        <constraint firstItem="Zr4-2w-DUW" firstAttribute="centerY" secondItem="DL9-p1-0kk" secondAttribute="centerY" id="l1i-yJ-bVb"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="15"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <outletCollection property="gestureRecognizers" destination="gjD-bH-8ev" appends="YES" id="eQp-6p-ONX"/>
                    </connections>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FhK-fN-dE3">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="42"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="42" id="Cds-xR-mFA"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView hidden="YES" clipsSubviews="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_backtothetop_float" translatesAutoresizingMaskIntoConstraints="NO" id="Cgj-Fl-7yR">
                    <rect key="frame" x="340" y="711" width="41" height="41"/>
                    <gestureRecognizers/>
                    <constraints>
                        <constraint firstAttribute="width" constant="41" id="IOg-Jm-bav"/>
                        <constraint firstAttribute="width" secondItem="Cgj-Fl-7yR" secondAttribute="height" multiplier="1:1" id="jgp-Dp-Xmt"/>
                    </constraints>
                    <connections>
                        <outletCollection property="gestureRecognizers" destination="Q0u-jX-C29" appends="YES" id="rNB-1o-FkJ"/>
                    </connections>
                </imageView>
            </subviews>
            <color key="backgroundColor" red="0.94901960784313721" green="0.94901960784313721" blue="0.94901960784313721" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="DL9-p1-0kk" firstAttribute="top" secondItem="dUW-uW-oeX" secondAttribute="top" constant="57" id="0lW-pm-cJa"/>
                <constraint firstAttribute="bottom" secondItem="yhF-o0-4IY" secondAttribute="bottom" id="4W9-rp-K4U"/>
                <constraint firstItem="fb4-02-wJ5" firstAttribute="leading" secondItem="dUW-uW-oeX" secondAttribute="leading" id="6OO-Cs-ch7"/>
                <constraint firstAttribute="trailing" secondItem="Cgj-Fl-7yR" secondAttribute="trailing" constant="12" id="6p6-eL-1s9"/>
                <constraint firstAttribute="bottom" secondItem="fb4-02-wJ5" secondAttribute="bottom" id="7iN-gm-Njo"/>
                <constraint firstItem="DL9-p1-0kk" firstAttribute="leading" secondItem="dUW-uW-oeX" secondAttribute="leading" constant="24" id="CiR-iH-xfn"/>
                <constraint firstAttribute="trailing" secondItem="yhF-o0-4IY" secondAttribute="trailing" id="GMx-87-5mn"/>
                <constraint firstAttribute="bottom" secondItem="Cgj-Fl-7yR" secondAttribute="bottom" constant="100" id="Jr1-dk-41E"/>
                <constraint firstItem="yhF-o0-4IY" firstAttribute="leading" secondItem="dUW-uW-oeX" secondAttribute="leading" id="LvN-nd-ecr"/>
                <constraint firstAttribute="trailing" secondItem="fb4-02-wJ5" secondAttribute="trailing" id="YiX-qv-JZM"/>
                <constraint firstItem="FhK-fN-dE3" firstAttribute="top" secondItem="dUW-uW-oeX" secondAttribute="top" id="bub-Do-Tag"/>
                <constraint firstItem="FhK-fN-dE3" firstAttribute="leading" secondItem="dUW-uW-oeX" secondAttribute="leading" id="fBy-zH-JHa"/>
                <constraint firstAttribute="trailing" secondItem="FhK-fN-dE3" secondAttribute="trailing" id="jQD-TI-qbk"/>
                <constraint firstItem="fb4-02-wJ5" firstAttribute="top" secondItem="dUW-uW-oeX" secondAttribute="top" id="mWJ-UR-wm2"/>
                <constraint firstItem="yhF-o0-4IY" firstAttribute="top" secondItem="dUW-uW-oeX" secondAttribute="top" id="yrS-sR-oi5"/>
            </constraints>
            <point key="canvasLocation" x="79" y="-887"/>
        </view>
        <tapGestureRecognizer id="gjD-bH-8ev">
            <connections>
                <action selector="backClick:" destination="-1" id="G8I-Dj-Gq9"/>
            </connections>
        </tapGestureRecognizer>
        <tapGestureRecognizer id="Q0u-jX-C29">
            <connections>
                <action selector="scrowToTop:" destination="-1" id="EJ1-10-4zR"/>
            </connections>
        </tapGestureRecognizer>
    </objects>
    <resources>
        <image name="icon_backtothetop_float" width="44.666667938232422" height="44.666667938232422"/>
        <image name="icon_return_listpage_n" width="11" height="19.666666030883789"/>
    </resources>
</document>
