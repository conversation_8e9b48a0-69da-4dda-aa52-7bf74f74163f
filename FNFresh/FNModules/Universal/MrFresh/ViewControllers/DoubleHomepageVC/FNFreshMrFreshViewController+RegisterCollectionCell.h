//
//  FNFreshMrFreshViewController+RegisterCollectionCell.h
//  FNFresh
//
//  Created by wang<PERSON> on 2021/2/3.
//  Copyright © 2021 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshViewController.h"
#import "FNFreshMrFreshCollectionViewFlowLayout.h"
//cells
#import "FNMrFreshBoldTitleCornerCollectionHeaderView.h"
#import "FNFreshMrFreshImageTitleHeaderCollectionReusableView.h"
#import "FNFreshMrFreshMoreHeaderCollectionReusableView.h"
#import "FNFreshMrFreshPrintingHotStyleHeaderCollectionReusableView.h"
#import "FNFreshMrFreshFooterCollectionReusableView.h"
#import "FNFreshMrFreshShufflingBannerCollectionViewCell.h"
#import "FNFreshMrFreshClassificationNavigationCollectionViewCell.h"
#import "FNFreshMrFreshRecipeVideoCollectionViewCell.h"
#import "FNFreshMrFreshRecommendCollectionViewCell.h"
#import "FNFreshMrFreshOptimalSelectionFreshCollectionViewCell.h"
#import "FNFreshMrFreshFruitsVegetablesCollectionViewCell.h"
#import "FNFreshMrFreshSingleFigureCollectionViewCell.h"
#import "FNFreshMrFreshPictureUnitCollectionViewCell.h"
#import "FNFreshMrFreshAnnouncementCollectionViewCell.h"
#import "FNFreshMrFreshNewToTryHorizontalCollectionViewCell.h"
#import "FNFreshMrFreshNewToTryVerticalCollectionViewCell.h"
#import "FNFreshMrFreshPrintingHotStyleCollectionViewCell.h"
#import "FNFreshMrFreshManagerRecommendCollectionViewCell.h"
#import "FNFreshMrFreshVerticalLargeCommodityGroupCollectionViewCell.h"
#import "FNFreshMrFreshSparkleCollectionViewCell.h"
#import "FNFreshMrFreshTipCollectionViewCell.h"
#import "FNFreshMrFreshSaleCollectionViewCell.h"
#import "FNFreshMrFreshNewExclusiveCollectionViewCell.h"
#import "FNFreshMrFreshFloorClassificationCollectionViewCell.h"
#import "FNFreshMrFreshOftenBuyCell.h"
#import "FNFreshMrFreshCommonACollectionReusableView.h"
#import "FNFreshMrFreshEatInHomeSectionCell.h"
#import "FNFreshMrFreshIconCardCollectionViewCell.h"
#import "FNFreshMrFreshNewPriceCollectionViewCell.h"
#import "FNFreshMrFreshNewChoiceCollectionViewCell.h"
#import "FNFreshMrFreshNewComerCell.h"
#import "FNFreshMrFreshNoviceCouponCell.h"
#import "FNFreshMrFreshBrandZoneCollectionViewCell.h"
#import "FNFreshMrFreshCouponCenterCollectionCell.h"
#import "FNFreshMrFreshNewOfflineServiceCell.h"
#import "FNFreshMrFreshWaterfallFlowHeadReusableView.h"
#import "FNFreshMrFreshFeedsCollectionViewCell.h"
#import "FNFreshMrFreshHotStyleHorizontalCollectionViewCell.h"
#import "FNFreshNewFreshSaleCollectionViewCell.h"
#import "FNFreshMrFreshSuperShoppingCollectionViewCell.h"
#import "FNFreshMrFreshNewOftenBuyCollectionViewCell.h"
#import "FNMrFreshFourFunctionCollectionCell.h"
#import "FNFreshShoppingCardCell.h"
#import "FNFreshMrFreshRegularCustomerNXOneCell.h"
#import "FNFreshMrFreshGetCouponCollectionCell.h"
#import "FNFreshMrFreshGetCouponSimpleStyleCell.h"
#import "FNFreshBrandZoneAndCouponCenterJointCell.h"
#import "FNFreshMrFreshTaskBannerCollectionViewCell.h"
#import "FNFreshNewUnitModuleCollectionViewCell.h"
#import "FNFreshMrFreshNewHotStyleCollectionViewCell.h"
#import "FNFreshFourGridCollectionViewCell.h"
#import "FNFreshMrFreshBaseCollectionView.h"
#import "FNFreshMrFreshSectionModel.h"

@interface FNFreshMrFreshViewController (RegisterCollectionCell)<UICollectionViewDataSource, FNMrFreshCollectionViewDelegateFlowLayout,FNFreshMrFreshUpdateHeightDelegate>

/// 加购的子cell，爆款专区，feeds
@property (nonatomic, strong) UICollectionViewCell *addShoppingCartCell;
@property (nonatomic, strong) id addToCartModel;
@property (nonatomic, assign) BOOL isFeeds;

//注册cell
- (void)registerCollectionReusableView;

@end

