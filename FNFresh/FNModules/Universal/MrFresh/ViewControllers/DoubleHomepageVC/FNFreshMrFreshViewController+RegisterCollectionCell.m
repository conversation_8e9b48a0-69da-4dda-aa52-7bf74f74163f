//
//  FNFreshMrFreshViewController+RegisterCollectionCell.m
//  FNFresh
//
//  Created by wang<PERSON> on 2021/2/3.
//  Copyright © 2021 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshViewController+RegisterCollectionCell.h"
#import "FNFreshMrFreshViewModel.h"
#import "FNFreshMrFreshCellFactory.h"
#import "FNMediator+FreshAddressModule.h"
#import "FNFreshTabBarController.h"
#import "FNBlurImageView.h"
#import "UIResponder+FNHomeEvent.h"
#import "SDWebImageManager.h"
#import "UIImage+FNOptimize.h"
#import "MJRefresh.h"
#import <objc/runtime.h>

extern NSString *const UICollectionReusableViewIdentifier;
extern NSString *const FNFreshMrFreshFooterCollectionReusableViewIdentifier;
extern NSString *const FNFreshMrFreshCommonACollectionReusableViewIdentifier;
extern NSString *const FNFreshMrFreshMoreHeaderCollectionReusableViewIdentifier;
extern NSString *const FNFreshMrFreshPrintingHotStyleHeaderCollectionReusableViewIdentifier;
extern NSString *const FNFreshMrFreshShufflingBannerCollectionViewCellIdentifier;
extern NSString *const FNFreshMrFreshAnnouncementCollectionViewCellIdentifier;
extern NSString *const FNFreshMrFreshManagerRecommendCollectionViewCellIdentifier;

static const char *addShoppingCartKey = "addShoppingCartKey";
static const char *addToCartModelKey = "addToCartModelKey";
static const char *isFeedsKey = "isFeedsKey";
@implementation FNFreshMrFreshViewController (RegisterCollectionCell)

- (void)setAddShoppingCartCell:(UICollectionViewCell *)addShoppingCartCell {
    objc_setAssociatedObject(self, &addShoppingCartKey, addShoppingCartCell, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (UICollectionViewCell *)addShoppingCartCell {
    return objc_getAssociatedObject(self, &addShoppingCartKey);
}

- (void)setAddToCartModel:(id)addToCartModel {
    objc_setAssociatedObject(self, &addToCartModelKey, addToCartModel, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (id)addToCartModel {
    return objc_getAssociatedObject(self, &addToCartModelKey);
}

- (void)setIsFeeds:(BOOL)isFeeds {
    objc_setAssociatedObject(self, &isFeedsKey, [NSNumber numberWithBool:isFeeds], OBJC_ASSOCIATION_ASSIGN);
}

- (BOOL)isFeeds {
    return ((NSNumber *)objc_getAssociatedObject(self, &isFeedsKey)).boolValue;
}

//注册cell
- (void)registerCollectionReusableView {
    
    NSArray *headerViewClassArray = @[
        NSStringFromClass([FNFreshMrFreshMoreHeaderCollectionReusableView class]),
        NSStringFromClass([FNFreshMrFreshPrintingHotStyleHeaderCollectionReusableView class]),
        NSStringFromClass([FNFreshMrFreshCommonACollectionReusableView class]),
        NSStringFromClass([FNFreshMrFreshImageTitleHeaderCollectionReusableView class]),
        NSStringFromClass([FNFreshMrFreshWaterfallFlowHeadReusableView class]),
        NSStringFromClass([FNMrFreshBoldTitleCornerCollectionHeaderView class])
    ];
    for (NSString *headerViewClassString in headerViewClassArray) {
        
        [self.collectionView registerNib:[UINib nibWithNibName:headerViewClassString bundle:[FNFreshBundleHandler fnFreshBundle]] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:headerViewClassString];
    }
    
    [self.collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:UICollectionReusableViewIdentifier];
    [self.collectionView registerNib:[FNFreshMrFreshFooterCollectionReusableView fnFreshNib] forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:FNFreshMrFreshFooterCollectionReusableViewIdentifier];
    
    NSArray *cellClassArray = @[
        NSStringFromClass([FNFreshMrFreshShufflingBannerCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshClassificationNavigationCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshRecipeVideoCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshRecommendCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshOptimalSelectionFreshCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshFruitsVegetablesCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshFloorClassificationCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshSingleFigureCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshAnnouncementCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshNewToTryHorizontalCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshNewToTryVerticalCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshPrintingHotStyleCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshManagerRecommendCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshVerticalLargeCommodityGroupCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshSparkleCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshTipCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshSaleCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshNewExclusiveCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshOftenBuyCell class]),
        NSStringFromClass([FNFreshMrFreshEatInHomeSectionCell class]),
        NSStringFromClass([FNFreshMrFreshIconCardCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshNewPriceCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshNewChoiceCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshNewComerCell class]),
        NSStringFromClass([FNFreshMrFreshNoviceCouponCell class]),
        NSStringFromClass([FNFreshMrFreshBrandZoneCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshCouponCenterCollectionCell class]),
        NSStringFromClass([FNFreshMrFreshNewOfflineServiceCell class]),
        NSStringFromClass([FNFreshMrFreshFeedsCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshHotStyleHorizontalCollectionViewCell class]),
        NSStringFromClass([FNFreshNewFreshSaleCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshSuperShoppingCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshNewOftenBuyCollectionViewCell class]),
        NSStringFromClass([FNMrFreshFourFunctionCollectionCell class]),
        NSStringFromClass([FNFreshBrandZoneAndCouponCenterJointCell class]),
        NSStringFromClass([FNFreshMrFreshTaskBannerCollectionViewCell class]),
        NSStringFromClass([FNFreshNewUnitModuleCollectionViewCell class]),
        NSStringFromClass([FNFreshMrFreshPictureUnitCollectionViewCell class]),
        NSStringFromClass([FNFreshShoppingCardCell class]),
        NSStringFromClass([FNFreshMrFreshRegularCustomerNXOneCell class]),
        NSStringFromClass([FNFreshMrFreshGetCouponCollectionCell class]),
        NSStringFromClass([FNFreshMrFreshGetCouponSimpleStyleCell class]),
        NSStringFromClass([FNFreshMrFreshNewHotStyleCollectionViewCell class]),
        NSStringFromClass([FNFreshFourGridCollectionViewCell class])
    ];
    for (NSString *cellClassString in cellClassArray) {
        [self.collectionView registerNib:[UINib nibWithNibName:cellClassString bundle:[FNFreshBundleHandler fnFreshBundle]] forCellWithReuseIdentifier:cellClassString];
    }
}

- (void)responseHomeRecycleBannerImg:(NSURL *)imgUrl andBgColor:(NSString *)color {
    if (self.viewModel.responseModel.bgColor.length > 0) {
        self.collectionView.mj_header.backgroundColor = [UIColor clearColor];
        return;
    }
    if (color.length > 0 && !self.isNormalBgNavi) {
        [self.configBgView setBackgroundColor:[UIColor hexString:color]];
        [self.searchBtn setBackgroundColor:[UIColor hex:color]];
        if (!self.configBgView.isHidden) {
            self.collectionView.mj_header.backgroundColor = [UIColor hex:color];
        }
        self.bannerBgImgView.hidden = NO;
        WS(weakSelf)
        //        [[SDWebImageManager sharedManager] loadImageWithURL:imgUrl options:SDWebImageHighPriority progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
        //
        //            if (image) {
        //                // 1、缩放bg图片到屏幕宽度
        //                CGFloat scaleHeight = SCREEN_WIDTH * image.size.height / image.size.width;
        //                UIImage *resizeImg = [image fn_imageByResizeToSize:CGSizeMake(SCREEN_WIDTH, scaleHeight)];
        //                CATransition *transition = [CATransition animation];
        //                transition.duration = 0.5;
        //                transition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
        //                transition.type = kCATransitionFade;
        //                [weakSelf.bannerBgImgView.layer addAnimation:transition forKey:@"a"];
        //                weakSelf.bannerBgImgView.image = resizeImg;
        //            }
        //        }];
        
        [self.bannerBgImgView fn_setImageWithURL:imgUrl placeholder:nil completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            if (image) {
                // 1、缩放bg图片到屏幕宽度
                CGFloat scaleHeight = SCREEN_WIDTH * image.size.height / image.size.width;
                UIImage *resizeImg = [image fn_imageByResizeToSize:CGSizeMake(SCREEN_WIDTH, scaleHeight)];
                CATransition *transition = [CATransition animation];
                transition.duration = 0.5;
                transition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
                transition.type = kCATransitionFade;
                [weakSelf.bannerBgImgView.layer addAnimation:transition forKey:@"a"];
                weakSelf.bannerBgImgView.image = resizeImg;
            }
        }];
        
    } else {
        [self.configBgView setBackgroundColor:[UIColor hexString:@"#E60012"]];
        [self.searchBtn setBackgroundColor:[UIColor hex:@"#E60012"]];
    }
}

#pragma mark - collection view data source and delegate

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return [self.viewModel numberOfSections];
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self.viewModel numberOfItemsInSection:section];
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    __weak typeof(self)weakSelf = self;
    UICollectionViewCell *collectionViewCell = nil;
    FNMrFreshContentModel *contentModel = [self.viewModel contentModelWithSection:indexPath.section];
    
    NSString *cellIndentifier = NSStringFromClass([FNFreshMrFreshCellFactory cellWithModel:contentModel forIndexPath:indexPath]);
    FNFreshMrFreshBaseCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:cellIndentifier forIndexPath:indexPath];
    cell.indexRow = indexPath.row;
    cell.indexPath = indexPath;
    cell.delegate = self;
    if (contentModel.type == FNMrFreshContentModelTypeCategoryFloor ||
        contentModel.type == FNMrFreshContentModelTypeHorizontalLargeCommodityGroup ||
        contentModel.type == FNMrFreshContentModelTypeVerticalLargeCommodityGroup) {
        cell.indexOfSpecifiedModel = [self.viewModel indexOfSpecifiedModel:contentModel];
    }
    cell.baseViewController = self;
    cell.selectedIndexBlock = ^(NSInteger index) {
        if ([[[UIDevice currentDevice] systemVersion] floatValue] >= 9.0) {
            FNFreshMrFreshWaterfallFlowHeadReusableView *headView = (FNFreshMrFreshWaterfallFlowHeadReusableView *)[weakSelf.collectionView supplementaryViewForElementKind:UICollectionElementKindSectionHeader atIndexPath:indexPath];
            if (headView && [headView isKindOfClass:[FNFreshMrFreshWaterfallFlowHeadReusableView class]]) {
                [headView selectIndex:index];
            }
        } else {
            if (weakSelf.fallFlowHeaderView) {
                [weakSelf.fallFlowHeaderView selectIndex:index];
                
            }
        }
    };
    cell.storeTitleClicked = ^{
        if ([FNFreshUser shareInstance].isPractice) {
            UIViewController *viewController = [[FNMediator sharedInstance] getFreshChangeStoreModele_ChangeStoreViewControllerWithParameter:nil];
            [[FNFreshTabBarController shareInstance] pushViewController:viewController animated:YES];
        }
    };
    cell.isShowService = self.viewModel.responseModel.isShowStoreService;
    cell.addShoppingCart = ^(UICollectionViewCell *cell, BOOL isFeeds) {
        NSLog(@"cell is %@", NSStringFromClass([cell class]));
        //        NSLog(@"model is %@", NSStringFromClass([model class]));
        weakSelf.addShoppingCartCell = cell;
        weakSelf.isFeeds = isFeeds;
    };
    
    cell.noviceGiftCouponHandler = ^{
        [weakSelf.viewModel NewGiftJointBtnHidden];
        [weakSelf.collectionView reloadData];
    };
    cell.noviceCouponCountTimerDidComplete = ^{
        [weakSelf.viewModel removeNewGiftJointSectionModel];
        [weakSelf.collectionView reloadData];
    };
    cell.classificationIconsDidClickChangeStore = ^(FNFreshShopInfoModel *store) {
        [[FNFreshUser shareInstance] holdShopInfomationWithShopInfoModel:store errorType:0 needFresh:false];
    };
    [cell setupWithDataModel:contentModel
                     handler:^(FNFreshMrFreshOpenPageType type,
                               FNMrFreshGoodsModel * _Nullable goodsModel,
                               UIImageView * _Nullable imageView,
                               NSString * _Nullable linkUrl) {
        switch (type) {
            case FNFreshMrFreshOpenPageTypeLookTheDetails:
            case FNFreshMrFreshOpenPageTypeLinkUrl: {
                [weakSelf openPageWithURLString:linkUrl];
            } break;
            case FNFreshMrFreshOpenPageTypeAddToCart: {
                weakSelf.addToCartModel = goodsModel;
                NSString *source = nil;
                // 新人n选1和新人专项价合并为FNMrFreshContentModelTypeNewGiftJoint
                // 这里借linkUrl字段，判断0，1，1代表N选1, 3代表老人N选1
                if (contentModel.type == FNMrFreshContentModelTypeNewGiftJoint ||
                    contentModel.type == FNMrFreshContentModelTypeRegularCustomerNXOne) {
                    if ([linkUrl isEqualToString:@"1"]) {
                        goodsModel.goodsToShopCart.activityId = contentModel.activityId;
                        source = @"1";
                    } else if ([linkUrl isEqualToString:@"3"]) {
                        goodsModel.goodsToShopCart.activityId = contentModel.activityId;
                        source = @"3";
                    } else {
                        source = @"0";
                    }
                }
                [weakSelf setShopCartWithImageView:imageView
                                     andGoodsModel:goodsModel
                                            source:source];
                //                }
            }
                break;
            case FNFreshMrFreshOpenPageTypeLookAtAll: {
                if (contentModel.type == FNMrFreshContentModelTypeFreshSale &&
                    weakSelf.isShowSaleGuideTip) {
                    [weakSelf saleGuideTipDisappear];
                    return;
                }
                [weakSelf openPageWithURLString:linkUrl];
            }
                break;
        }
    }];
    
    if (contentModel.type == FNMrFreshContentModelTypeHotStyleHorizontal) {
        self.hotStyleZoneIndex = indexPath.section;
    } else if (contentModel.type == FNMrFreshContentModelTypeFreshSale || contentModel.type == FNMrFreshContentModelTypeNewFreshSale) {
        self.saleIndexPath = indexPath;
    } else if (contentModel.type == FNMrFreshContentModelTypeNewChoice) {
        self.nxyIndexPath = indexPath;
    } else if (contentModel.type == FNMrFreshContentModelTypeFeedsGoods) {
        self.feedsIndexPath = indexPath;
    } else if (contentModel.type == FNMrFreshContentModelTypeRegularCustomerNXOne) {
        self.oldNXOneIndexPath = indexPath;
    }
    
    collectionViewCell = cell;
    
    return collectionViewCell;
}


- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    
    __weak typeof(self)weakSelf = self;
    UICollectionReusableView *reusableView = nil;
    if ([kind isEqualToString:UICollectionElementKindSectionHeader]) {
        
        FNFreshMrFreshSectionModel *sectionModel = [self.viewModel sectionModelAtSection:indexPath.section];
        
        switch (sectionModel.headerViewType) {
                
            case FNFreshMrFreshHeaderViewTypeNormal: {
                
                reusableView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:UICollectionReusableViewIdentifier forIndexPath:indexPath];
            }
                break;
            case FNFreshMrFreshHeaderViewTypeBoldTitleCorner: {
                FNMrFreshBoldTitleCornerCollectionHeaderView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:NSStringFromClass([FNMrFreshBoldTitleCornerCollectionHeaderView class]) forIndexPath:indexPath];
                [headerView setupWithDataModel:sectionModel.contentModel handler:^(NSString *URLString) {
                    
                    [weakSelf openPageWithURLString:URLString];
                }];
                reusableView = headerView;
                break;
            }
                
            case FNFreshMrFreshHeaderViewTypeCommonA: {
                
                FNFreshMrFreshCommonACollectionReusableView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:FNFreshMrFreshCommonACollectionReusableViewIdentifier forIndexPath:indexPath];
                [headerView setupWithDataModel:sectionModel.contentModel handler:^(NSString *URLString) {
                    
                    [weakSelf openPageWithURLString:URLString];
                }];
                reusableView = headerView;
            }
                break;
                
            case FNFreshMrFreshHeaderViewTypeTitle: {
                
                FNFreshMrFreshMoreHeaderCollectionReusableView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:FNFreshMrFreshMoreHeaderCollectionReusableViewIdentifier forIndexPath:indexPath];
                [headerView setupWithDataModel:sectionModel.contentModel handler:^(NSString *URLString) {
                    
                    [weakSelf openPageWithURLString:URLString];
                }];
                reusableView = headerView;
            }
                break;
                
            case FNFreshMrFreshHeaderViewTypePrintingHotStyle: {
                
                FNFreshMrFreshPrintingHotStyleHeaderCollectionReusableView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:FNFreshMrFreshPrintingHotStyleHeaderCollectionReusableViewIdentifier forIndexPath:indexPath];
                [headerView setupWithDataModel:sectionModel.contentModel handler:^(NSString *URLString) {
                    
                    [weakSelf openPageWithURLString:URLString];
                }];
                reusableView = headerView;
            }
                break;
                
            case FNFreshMrFreshHeaderViewTypeImageTitle: {
                FNFreshMrFreshImageTitleHeaderCollectionReusableView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:NSStringFromClass([FNFreshMrFreshImageTitleHeaderCollectionReusableView class]) forIndexPath:indexPath];
                [headerView configureWithImage:sectionModel.contentModel.moduleTitleImage title:sectionModel.contentModel.moduleName];
                reusableView = headerView;
            }
                break;
            case FNFreshMrFreshHeaderViewTypeClassificationTab: {
                FNFreshMrFreshWaterfallFlowHeadReusableView *headView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:NSStringFromClass([FNFreshMrFreshWaterfallFlowHeadReusableView class]) forIndexPath:indexPath];
                WS(weakSelf)
                [headView setupWithDataModel:sectionModel.contentModel handler:^(NSUInteger index) {
                    FNFreshMrFreshFeedsCollectionViewCell *cell = (FNFreshMrFreshFeedsCollectionViewCell *)[weakSelf.collectionView cellForItemAtIndexPath:indexPath];
                    if (cell && [cell isKindOfClass:[FNFreshMrFreshFeedsCollectionViewCell class]]) {
                        [cell selectIndex:index];
                    }
                }];
                reusableView = headView;
                self.fallFlowHeaderView = headView;
            }
                break;
        }
        
    } else {
        
        FNFreshMrFreshFooterCollectionReusableView *footerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:FNFreshMrFreshFooterCollectionReusableViewIdentifier forIndexPath:indexPath];
        reusableView = footerView;
    }
    return reusableView;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    
    return [self.viewModel referenceSizeForHeaderInSection:section];
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section {
    
    return [self.viewModel referenceSizeForFooterInSection:section];
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    return [self.viewModel sizeForItemAtIndexPath:indexPath];
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout spaceAtSection:(NSInteger)section {
    
    return [self.viewModel spaceAtSection:section];
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    
    return [self.viewModel insetForSectionAtIndex:section];
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    
    return [self.viewModel minimumLineSpacingForSectionAtIndex:section];
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    
    return [self.viewModel minimumInteritemSpacingForSectionAtIndex:section];
}

- (UIColor *)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout backgroundColorForSection:(NSInteger)section {
    
    return [self.viewModel backgroundColorForSection:section];
}

- (NSString *)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout bgImgUrlForSection:(NSInteger)section {
    return [self.viewModel bgImgUrlForSection:section];
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {
    
    if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshShufflingBannerCollectionViewCellIdentifier]) {
        
        [(FNFreshMrFreshShufflingBannerCollectionViewCell *)cell changeTimerState:YES];
    } else if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshManagerRecommendCollectionViewCellIdentifier]) {
        
        [(FNFreshMrFreshManagerRecommendCollectionViewCell *)cell changeTimerState:YES];
    } else if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshAnnouncementCollectionViewCellIdentifier]) {
        [(FNFreshMrFreshAnnouncementCollectionViewCell *)cell changeTimerState:YES];
    }
}

- (void)collectionView:(UICollectionView *)collectionView didEndDisplayingCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {
    
    if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshShufflingBannerCollectionViewCellIdentifier]) {
        
        [(FNFreshMrFreshShufflingBannerCollectionViewCell *)cell changeTimerState:NO];
    } else if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshManagerRecommendCollectionViewCellIdentifier]) {
        
        [(FNFreshMrFreshManagerRecommendCollectionViewCell *)cell changeTimerState:NO];
    } else if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshAnnouncementCollectionViewCellIdentifier]) {
        [(FNFreshMrFreshAnnouncementCollectionViewCell *)cell changeTimerState:NO];
    }
}

- (void)collectionView:(UICollectionView *)collectionView willDisplaySupplementaryView:(UICollectionReusableView *)view forElementKind:(NSString *)elementKind atIndexPath:(NSIndexPath *)indexPath {
    
    if (![elementKind isEqualToString:UICollectionElementKindSectionFooter]) {
        
        return;
    }
    CGPoint point = [collectionView.panGestureRecognizer translationInView:collectionView];
    if (point.y >= 0) {
        
        return;
    }
    FNMrFreshContentModel *contentModel = [self.viewModel contentModelWithSection:indexPath.section];
    
    if (contentModel.type == FNMrFreshContentModelTypeOftenBuy) {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"114001",
            @"page_id" :[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"6",
        }];
    }
    
    if (contentModel.type != FNMrFreshContentModelTypeHorizontalLargeCommodityGroup &&
        contentModel.type != FNMrFreshContentModelTypeCategoryFloor &&
        contentModel.type != FNMrFreshContentModelTypeVerticalLargeCommodityGroup) {
        
        return;
    }
    NSDictionary *dictionary = @{
        @"floor_name":contentModel.moduleName ?: @"",
    };
    NSString *remarks = [dictionary dictionaryToJSON];
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"100064",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"6",
        @"remarks":remarks,
    }];
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    FNMrFreshContentModel *contentModel = [self.viewModel contentModelWithSection:indexPath.section];
    NSString *idx = [NSString stringWithFormat:@"%ld",indexPath.item + 1];
    switch (contentModel.type) {
            
        case FNMrFreshContentModelTypeOnePlusOne: {
            
            FNMrFreshPictureModel *dataModel = [contentModel.picList safeObjectAtIndex:indexPath.row];
            [self openPageWithURLString:[dataModel linkUrl]];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"153017",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
                @"col_position":idx,
            }];
        }
            break;
            
        case FNMrFreshContentModelTypeSingleFigure: {
            
            FNMrFreshPictureModel *dataModel = contentModel.picList.firstObject;
            [self openPageWithURLString:[dataModel linkUrl]];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"104006",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
                @"col_pos_content":dataModel.linkUrl ?: @"",
            }];
        }
            break;
            
        case FNMrFreshContentModelTypeCollectPoints: {
            FNMrFreshPictureModel *dataModel = contentModel.picList.firstObject;
            [self openPageWithURLString:[dataModel linkUrl]];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"122008",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
            }];
        }
            break;
        case FNMrFreshContentModelTypePrintingHotStyle: {
            
            FNMrFreshGoodsModel *dataModel = [contentModel.goodsList safeObjectAtIndex:indexPath.item];
            [self openPageWithURLString:dataModel.linkUrl];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"106032",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
                @"col_pos_content":dataModel.goodsID,
            }];
        }
            break;
            
        case FNMrFreshContentModelTypeNewProductRecommendations: {
            
            FNMrFreshGoodsModel *dataModel = [contentModel.goodsList safeObjectAtIndex:indexPath.item];
            [self openPageWithURLString:dataModel.linkUrl];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"106037",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
                @"col_pos_content":dataModel.goodsID ?: @"",
            }];
        }
            
            break;
            
        case FNMrFreshContentModelType1P2Banner: {
            
            FNMrFreshPictureModel *dataModel = [contentModel.picList safeObjectAtIndex:indexPath.row];
            [self openPageWithURLString:dataModel.linkUrl];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"153018",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
                @"col_position":idx,
            }];
        }
            break;
            
        case FNMrFreshContentModelType2P2Banner: {
            
            FNMrFreshPictureModel *dataModel = [contentModel.picList safeObjectAtIndex:indexPath.row];
            [self openPageWithURLString:dataModel.linkUrl];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"153019",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
                @"col_position":idx,
            }];
        }
            break;
        case FNMrFreshContentModelTypeBigStorm: {
            FNMrFreshPictureModel *dataModel = contentModel.picList.firstObject;
            [self openPageWithURLString:[dataModel linkUrl]];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"135040",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
            }];
        }
            break;
        case FNMrFreshContentModelTypeFreshTaskBanner: {
            [self openPageWithURLString:contentModel.hotLinkUrl];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"150022",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
            }];
        }
            break;
        default:
            break;
    }
}

- (void)updateCellHeightWith:(FNFreshMrFreshBaseCollectionCell *)cell indexPath:(NSIndexPath *)indexPath {
    if ([cell isKindOfClass:[FNFreshMrFreshSingleFigureCollectionViewCell class]]) {
        if (indexPath && (indexPath.section < [self.viewModel numberOfSections])) {
            WS(weakSelf);
            [UIView performWithoutAnimation:^{
                [weakSelf.collectionView reloadItemsAtIndexPaths:@[indexPath]];
            }];
        }
    }
}

@end
