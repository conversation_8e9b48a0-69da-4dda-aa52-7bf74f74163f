//
//  FNFreshMrFreshViewController+popupWindow.m
//  FNFresh
//
//  Created by wang<PERSON> on 2021/2/3.
//  Copyright © 2021 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshViewController+popupWindow.h"
#import "FNFreshMrFreshPopupWindowParameterModel.h"
#import "NSObject+FNFreshEncryptPersistence.h"
#import <FNCacheManager.h>
#import "FNFreshMrFreshService.h"
#import "FNFreshMrFreshViewModel.h"
#import "FNFreshMrFreshNoviceGiftAlertViewController.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "FNFreshMrFreshNewGuidanceViewController.h"
#import "FNFreshMrFreshTooltipView.h"
#import "FNFreshMrFreshPopupWindowViewController.h"
#import "FNFreshMrFreshOrdinaryCouponsViewController.h"
#import "FNMediator+FreshCouponModel.h"
#import "FNFreshMrFreshGradientView.h"
#import "FNFreshTabBarController.h"
#import "FNFreshMrFreshTaskViewController.h"
#import "FNFreshMrFreshVersionUpdateViewController.h"
#import "FNFreshMrFreshVersionUpdateResponseModel.h"
#import "FNFreshMrFreshVersionUpdateParameterModel.h"
#import "FNFreshMrFreshACAnnouncementResponseModel.h"
#import "FNFreshMrFreshH5AnnouncementViewController.h"
#import "FNFreshNewStoreGiftPopWindowViewController.h"
#import "FNFreshNewStoreHomePopGiftWindowResponseModel.h"
#import "FNFreshCouponViewController.h"
#import "FNFreshNotificationPopWidowViewController.h"
#import "FNFreshMrFreshCRMCouponPopWindowVC.h"
#import "FNFreshNewcomerChannelViewController.h"
#import "FNFreshMrFreshBombVoucherViewController.h"
#import "FNFreshMrFreshMutiBombVoucherViewController.h"
#import "FNFreshMrFreshSaveMoneyCardViewController.h"
#import "FNFreshMrFreshCustomerNXOneViewController.h"

extern NSString *const FNMrFreshLastHomePopupWindowTime;
extern NSString *const FNMrFreshLastHomePopupWindowSchedule;
extern NSString *const FNMrFreshLastHomePopupIndex;
extern NSString *const FNMrFreshLastBirthdayBombTime;
extern NSString *const FNMrFreshLastNewGuidancePopupWindowTime;
extern NSString *const FNMrFreshLastCommonPopupWindowTime;
extern NSString *const FNMrFreshLastCommonPopupWindowSchedule;
extern NSString *const FNMrFreshLastAppOpenTime;
extern NSString *const FNMrFreshACToGoAnnouncement;
extern NSString *const FNMrFreshACToGoAnnouncementForDay;
extern NSString *const FNMrFreshACToGoAnnouncementNoRemind;
extern NSString *const FNMrFreshOpenGiftPopupTime;
extern NSString *const FNMrFreshCRMCouponPopupTime;
extern NSString *const FNMrFreshCouponBombTime;
extern NSString *const FNMrFreshFirstEnterAppPushNot;
extern NSString *const FNMrFreshSaveMoneyCardTime;
extern NSString *const FNMrFreshOldNForOneTime;

/*
 弹窗券模块统一规则
 1、金额：满减、单品定价、单品立减、抵用、运费、自提、省钱卡券展示【券金额】；满折展示【券折扣度】；赠品/礼品展示【赠品券/礼品券】，数字最多取小数位后两位，后位去0显示
 2、券名称：取券活动名称，最多一行，超出...
 3、门槛：满减、满折、单品立减、运费展示【满XX元可用，无门槛展示“无门槛”，数字最多取小数位后两位，后位去0显示】；单品定价展示【单品价】；抵用、自提展示【无门槛】；省钱卡券展示【买省钱卡可用】；赠品/礼品不展示
 */
@implementation FNFreshMrFreshViewController (popupWindow)

- (void)requestPopupWindowInfo {
    
    /* return 条件
     1.存在版本更新弹窗
     2.首页homePage请求数据为nil
     3.定位未结束
     */
    if (!self.isVersionUpdateFinish ||
        !self.viewModel.responseModel ||
        ![FNFreshUser shareInstance].locationFinished || 
        self.viewModel.responseModel.pageFault ||
        self.isNewOrMiniError || 
        !self.isCurrentPage) {
        return;
    }

    // 容错页状态需要单独计算  不在这里标记
    if (!self.faultToleranceView) {
        self.handlePopwindow = YES;
    }
    
    __weak typeof(self)weakSelf = self;
    if (![[FNCacheManager shareMananger] firstOpenForPage:NSStringFromClass([self class])]) {
        [self writeToFile:@(0) key:FNMrFreshLastAppOpenTime];
    }
    
    FNFreshMrFreshPopupWindowParameterModel *parameterModel = [[FNFreshMrFreshPopupWindowParameterModel alloc] init];
    parameterModel.isFirstLaunchApp = !self.isFirstEntryShop;
    parameterModel.storeCode = [FNFreshUser shareInstance].shopId;
    parameterModel.popupBombPeriods = [self readFromFile:FNMrFreshLastHomePopupWindowSchedule];
    parameterModel.popupBombIndex = [[self readFromFile:FNMrFreshLastHomePopupIndex] integerValue];
    parameterModel.noviciateGiftBombTime = [[self readFromFile:FNMrFreshLastNewGuidancePopupWindowTime] doubleValue];
    parameterModel.popupBombTime = [[self readFromFile:FNMrFreshLastHomePopupWindowTime] doubleValue];
    parameterModel.commonCouponBombTime = [[self readFromFile:FNMrFreshLastCommonPopupWindowTime] doubleValue];
    parameterModel.commonCouponBombPeriods = [self readFromFile:FNMrFreshLastCommonPopupWindowSchedule];
    parameterModel.appOpenLastTime = [[self readFromFile:FNMrFreshLastAppOpenTime] doubleValue];
    parameterModel.openGiftPopupTime = [[self readFromFile:FNMrFreshOpenGiftPopupTime] doubleValue];
    parameterModel.crmCouponBombTime = [[self readFromFile:FNMrFreshCRMCouponPopupTime] doubleValue];
    parameterModel.birthdayCouponBombTime = [[self readFromFile:FNMrFreshLastBirthdayBombTime] doubleValue];
    parameterModel.couponBombTime = [[self readFromFile:FNMrFreshCouponBombTime] doubleValue];
//    parameterModel.saveMoneyCardTime = [[self readFromFile:FNMrFreshSaveMoneyCardTime] doubleValue];
    parameterModel.oldNForOneTime = [[self readFromFile:FNMrFreshOldNForOneTime] doubleValue];

    [FNFreshMrFreshService requestMrFreshPopupWindowWithParameter:parameterModel success:
     ^(id responseObject, BOOL isCache) {
        // 处理response
        [weakSelf handlePopupWindowWithPopupWindowResponseModel:responseObject];
    } failure:^(id responseObject, NSError *error) {
        [weakSelf handlePopupWindowWithPopupWindowResponseModel:nil];
    }];
}

- (void)requestForVersionUpdate
{
    FNFreshMrFreshVersionUpdateParameterModel *parameterModel = [[FNFreshMrFreshVersionUpdateParameterModel alloc] init];
    parameterModel.check_by = @"app";
    parameterModel.last_update_appVersionNo = [FNFreshMrFreshVersionUpdateViewController getIgnoreVersion];
    
    parameterModel.network =
    ([[FNNetworkClient freshClient].networkReachablity currentReachabilityStatus] == FNReachableViaWWAN) ? 2 : 1;
    
    __weak typeof(self)weakSelf = self;
    
    [FNFreshMrFreshService requestForUpdateVersionWithParameter:parameterModel success:
     ^(FNFreshMrFreshVersionUpdateResponseModel *responseObject, BOOL isCache) {
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"updateSuccess"];
        
        if (responseObject.appVersionNo) {
            [weakSelf presentVersionUpdateControllerWithUpdateResponseModel:responseObject];
        } else {
            weakSelf.versionUpdateFinish = YES;
            [weakSelf showAddressWarning];
            
            if (!weakSelf.isHandlePopwindow) {
                [weakSelf requestPopupWindowInfo];
            }
        }
    } failure:^(id responseObject, NSError *error) {
        weakSelf.versionUpdateFinish = YES;
        [weakSelf showAddressWarning];
        
        if (!weakSelf.isHandlePopwindow) {
            [weakSelf requestPopupWindowInfo];
        }
    }];
}

// 欧尚合并弹框提示
- (void)requestACToGoAnnouncementPop 
{
    NSString *valueStr = (NSString *)[[FNCacheManager shareMananger] cacheObjectWithKey:FNMrFreshACToGoAnnouncementNoRemind];
    if ([valueStr isEqualToString:@"1"]) { // 不再提醒
        return;
    }
    
    NSDate *date = (NSDate *)[[FNCacheManager shareMananger] cacheObjectWithKey:FNMrFreshACToGoAnnouncementForDay];
    if (date && [self isSameDay:date and:[NSDate date]]) { // 每个自然日只弹一次
        return;
    }
    
    WS(weakSelf)
    [FNFreshMrFreshService requestACToGoAnnouncementPopWithStatus:@"1"
                                                          success:
     ^(FNFreshMrFreshACAnnouncementResponseModel *responseObject, BOOL isCache) {
        
        [weakSelf presentACToGoAnnouncementVCWithResponseModel:responseObject];
    } failure:^(id responseObject, NSError *error) {
        
        [weakSelf presentNewGuidanceViewController];
    }];
}

- (void)handlePopupWindowWithPopupWindowResponseModel:(FNFreshMrFreshPopupWindowResponseModel *)responseModel {
    if (!self.isCurrentPage) {
        //如果已经跳转到其他页面  这里就不弹窗
        return;
    }
    //建议更新弹窗>新人礼包弹窗>爆红包弹窗>会员生日弹窗>CRM充券弹窗>老人N选一爆屏>普通优惠券弹窗>省钱卡弹窗>促销图片弹窗>push开启引导弹窗，每个进程仅有以上一个出现 api控制优先级
    //说明是新手礼包
    if (responseModel.noviciateGift) {
        [self presentNoviceGiftBagViewControllerWithNewGuidanceModel:responseModel.noviciateGift];
        [self writeToFile:@(responseModel.noviciateGift.noviciateGiftBombTime) key:FNMrFreshLastNewGuidancePopupWindowTime];
       
        if (self.faultToleranceView) {
            self.popupWindowInFaulTolerance = YES;
        }
        // 说明是广告弹窗或者生日弹框
    } else if (responseModel.popup || responseModel.birthdayPopup) {
        FNMrFreshPopupWindowHomeModel *model = responseModel.birthdayPopup;
        if (responseModel.popup) {
            model = responseModel.popup;
        }
        
        [self presentPopupWindowViewControllerWithPopupWindowHomeModel:model isBirtday:responseModel.birthdayPopup];
       
        if (responseModel.popup) {
            [self writeToFile:@(responseModel.popup.popupBombTime) key:FNMrFreshLastHomePopupWindowTime];
            [self writeToFile:responseModel.popup.popupBombPeriods key:FNMrFreshLastHomePopupWindowSchedule];
            [self writeToFile:@(responseModel.popup.popupBombIndex) key:FNMrFreshLastHomePopupIndex];
        } else {
            [self writeToFile:@(responseModel.birthdayPopup.popupBombTime) key:FNMrFreshLastBirthdayBombTime];
        }
        // 说明是普通优惠券弹窗
    } else if (responseModel.commonCoupon) {
        [self presentOrdinaryCouponsViewControllerWithDataModel:responseModel.commonCoupon];
        [self writeToFile:@(responseModel.commonCoupon.commonCouponBombTime) key:FNMrFreshLastCommonPopupWindowTime];
        [self writeToFile:responseModel.commonCoupon.commonCouponBombPeriods key:FNMrFreshLastCommonPopupWindowSchedule];
    } else if (responseModel.freshTask) {
        // 鲜任务
        [self presentFreshTaskViewControllerWithData:responseModel.freshTask];
        [self writeToFile:@([[NSDate dateWithTimeIntervalSinceNow:0] timeIntervalSince1970]) key:FNMrFreshLastAppOpenTime];
    } else if (responseModel.openGift) {
        // 开业礼包弹窗
        [self writeToFile:@(responseModel.openGift.openGiftPopupTime) key:FNMrFreshOpenGiftPopupTime];
        FNFreshNewStoreHomePopGiftWindowResponseModel *model = [[FNFreshNewStoreHomePopGiftWindowResponseModel alloc] init];
        model.couponList = responseModel.openGift.couponList;
        [self giftPopWindow:model];
    } else if (responseModel.crmCoupon) {
        // crm券
        [self presentCRMCouponViewControllerWithModel:responseModel.crmCoupon];
        [self writeToFile:@(responseModel.crmCoupon.crmCouponBombTime) key:FNMrFreshCRMCouponPopupTime];
    } else if (responseModel.bombCoupon) {
        // 爆红包
        [self presentCouponBombViewControllerWithModel:responseModel.bombCoupon isBombing:NO];
        [self writeToFile:@(responseModel.bombCoupon.couponBombTime) key:FNMrFreshCouponBombTime];
    } else if (responseModel.saveMoneyCard) {
        // 省钱卡
        [self presentSaveMoneyPopupWindowVCWithData:responseModel.saveMoneyCard];
        [self writeToFile:@(responseModel.saveMoneyCard.popupBombTime)
                      key:FNMrFreshSaveMoneyCardTime];
    } if (responseModel.oldNForOne) {
        // 老人N选1
        [self presentOldNForOneVCWithResponseModel:responseModel.oldNForOne];
        [self writeToFile:@(responseModel.oldNForOne.oldNForOneTime)
                      key:FNMrFreshOldNForOneTime];
    } else {
        // 容错页不弹新手指导
        if (self.faultToleranceView) {
            return;
        }
        [self openRemotePushNot];
        [self presentNewGuidanceViewController];
    }

    WKWebView *currentWebView = [self getWebView];
    if (currentWebView) {
        // 想要将web氛围层确保移到弹框之上
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[UIApplication sharedApplication].delegate.window bringSubviewToFront:currentWebView];
        });
    }
}

//开业礼包弹窗
- (void)giftPopWindow:(FNFreshNewStoreHomePopGiftWindowResponseModel *)dataModel 
{
    WS(weakSelf)
    FNFreshNewStoreGiftPopWindowViewController *vc = 
    [FNFreshNewStoreGiftPopWindowViewController instanceWithDataModel:dataModel isNew:NO
                                                              handler:^(BOOL dismiss) {
        if (!dismiss) {
            // 跳我的优惠券
            FNFreshCouponViewController *vc = [[FNFreshCouponViewController alloc] initWithCouponClass:FNFreshCouponClassCoupon];
            [FNFreshTabBarController pushViewController:vc animated:YES];
            
        } else {
            [weakSelf presentNewGuidanceViewController];
        }
    }];
    
    CGFloat maxHeight = IS_IPHONE_5?450:500;
    CGFloat countHeight = 270 + dataModel.couponList.count * 90;
    [self fn_presentViewController:vc 
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                          viewSize:CGSizeMake(315, MIN(maxHeight, countHeight))
                          duration:0.75
                             alpha:0.5
                            handle:nil];
}

// 新手礼包
- (void)presentNoviceGiftBagViewControllerWithNewGuidanceModel:(FNMrFreshPopupWindowNewGuidanceModel *)newGuidanceModel
{
    __weak typeof(self)weakSelf = self;
    FNFreshMrFreshNoviceGiftAlertViewController *viewController = 
    [FNFreshMrFreshNoviceGiftAlertViewController initWithNewGuidanceModel:newGuidanceModel
                                                                   handle:^(BOOL dismiss) {
        if (!dismiss) {
            if ([FNFreshUser shareInstance].isLogin) {
                // 已登录状态 按钮名称为立即查看 跳转到新人频道页
                FNFreshNewcomerChannelViewController *vc = [FNFreshNewcomerChannelViewController new];
                [[FNFreshTabBarController shareInstance] pushViewController:vc animated:YES];
            } else {
                [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
                    if (weakSelf.sendGiftURL) {
                        // 有URL直接跳转
                        [weakSelf openPageWithURLString:weakSelf.sendGiftURL];
                        weakSelf.sendGiftURL = nil;
                    } else {
                        // 标记为需要跳转状态 此状态下requestForSendGiftOnLoad会进行跳转
                        weakSelf.sendGiftURL = @"jump";
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), 
                                       dispatch_get_main_queue(), ^{
                            // 标记为不需要跳转
                            weakSelf.sendGiftURL = @"stop";
                        });
                    }
                }];
            }
        } else {
            [weakSelf presentNewGuidanceViewController];
        }
    }];
    
    CGFloat width = 375;
    CGFloat height = 452;
    [self fn_presentViewController:viewController 
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                          viewSize:CGSizeMake(width, height)
                          duration:0.75
                             alpha:0.5
                            handle:nil];
}

/// 省钱卡弹框
- (void)presentSaveMoneyPopupWindowVCWithData:(FNMrFreshPopupSaveMoneyCardModel *)model {

    WS(weakSelf)
    FNFreshMrFreshSaveMoneyCardViewController *vc = [FNFreshMrFreshSaveMoneyCardViewController initWithData:model handler:^(NSString *link) {
        if (!link) {
            [weakSelf presentNewGuidanceViewController];
        } else {
            [weakSelf openPageWithURLString:link];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"203015",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
                @"col_position":[NSString stringWithFormat:@"%ld",model.popupType]
            }];
        }
    }];
    
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"203014",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"6",
        @"col_position":[NSString stringWithFormat:@"%ld",model.popupType]
    }];

    [self fn_presentViewController:vc
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFromBottom
                          viewSize:[UIScreen mainScreen].bounds.size
                          duration:0.55
                             alpha:0.5
                            handle:nil];
}

// 广告弹窗 & 生日弹框
- (void)presentPopupWindowViewControllerWithPopupWindowHomeModel:(FNMrFreshPopupWindowHomeModel *)popupWindowHomeModel
                                                       isBirtday:(BOOL)isBirtday
{
    NSURL *URL = [NSURL URLWithString:popupWindowHomeModel.wImgUrl];
    if (popupWindowHomeModel.imgUrl.length > 0 && isBirtday) {
        URL = [NSURL URLWithString:popupWindowHomeModel.imgUrl];
    }
    
    __weak typeof(self)weakSelf = self;
    [[SDWebImageManager sharedManager] loadImageWithURL:URL
                                                options:SDWebImageAvoidDecodeImage
                                                context:@{SDWebImageContextStoreCacheType:@(SDImageCacheTypeMemory)}
                                               progress:nil
                                              completed:^(UIImage * _Nullable image,
                                                          NSData * _Nullable data,
                                                          NSError * _Nullable error,
                                                          SDImageCacheType cacheType,
                                                          BOOL finished,
                                                          NSURL * _Nullable imageURL) {
        if (!image) {
            return;
        }
        
        if (!self.isCurrentPage) {
            return;
        }
        
        FNFreshMrFreshPopupWindowViewController *viewController =
        [FNFreshMrFreshPopupWindowViewController instanceWithImageURL:URL
                                                           isBirthday:isBirtday
                                                              handler:^(BOOL dismiss) {
            if (dismiss) {

                [weakSelf presentNewGuidanceViewController];
            } else {
                
                [weakSelf openPageWithURLString:popupWindowHomeModel.linkUrl];
            }
        }];
        
        CGFloat width = [popupWindowHomeModel.picWidth floatValue] / 2 * Ratio;
        CGFloat height = [popupWindowHomeModel.picHeight floatValue] / 2 * Ratio;
        if (popupWindowHomeModel.imgUrl.length > 0) {
            width = SCREEN_WIDTH - 55;
            height = width * image.size.height / image.size.width;
        }
        
        CGSize windowSize = CGSizeMake(width, height + 47);
        [weakSelf fn_presentViewController:viewController
                         customAnimateType:FNFreshMrFreshCustomAnimateTypeFromBottom
                                  viewSize:windowSize
                                  duration:0.55
                                     alpha:0.5
                                    handle:nil];
    }];
}

//普通优惠券
- (void)presentOrdinaryCouponsViewControllerWithDataModel:(FNMrFreshPopupWindowCommonOrdinaryModel *)dataModel {
    
    self.haveShowedCouponWindow = 1;
    __weak typeof(self)weakSelf = self;
    FNFreshMrFreshOrdinaryCouponsViewController *viewController = 
    [FNFreshMrFreshOrdinaryCouponsViewController instanceWithNewGuidanceModel:dataModel
                                                                       handle:^(BOOL dismiss) {
        if (dismiss) {
            [weakSelf presentNewGuidanceViewController];
        } else {

            if ([FNFreshUser shareInstance].isLogin) {
                
                UIViewController *viewController =
                [[FNMediator sharedInstance] freshCouponModel_FNFreshCouponViewController_initWithCouponClass:FNFreshCouponClassCoupon];
                [FNFreshTabBarController pushViewController:viewController animated:YES];
                
            } else {
                
                [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
                    [[FNFreshTabBarController shareInstance] switchTabBarWithParam:@{@"tabItemIndex":@4}];
                }];
            }

        }
    }];
    
    CGFloat width = ceilf(320.f*Ratio);
    CGFloat height = ceilf(459.f*Ratio)+43;
    [self fn_presentViewController:viewController 
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                          viewSize:CGSizeMake(width, height)
                          duration:0.75
                             alpha:0.5
                            handle:nil];
}

- (void)presentFreshTaskViewControllerWithData:(FNMrFreshPopupWindowFreshTaskModel *)taskModel {
    
    WS(weakSelf)
    FNFreshMrFreshTaskViewController *viewController =
    [FNFreshMrFreshTaskViewController instanceWithFreshTaskModel:taskModel handler:^(BOOL dismiss) {
        if (dismiss) {
            [weakSelf presentNewGuidanceViewController];
        } else {
            [weakSelf openPageWithURLString:taskModel.linkUrl];
        }
    }];
    
    __weak typeof(viewController)weakVC = viewController;
    [self fn_presentViewController:viewController
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                          viewSize:CGSizeMake(280, 402)
                          duration:0.75
                             alpha:0.5
                            handle:^{
        [weakVC dismissViewControllerAnimated:YES completion:^{
            [weakSelf presentNewGuidanceViewController];
        }];
    }];
}

// CRM券弹框
- (void)presentCRMCouponViewControllerWithModel:(FNMrFreshPopupWindowNewGuidanceModel *)model
{
    WS(weakSelf)
    FNFreshMrFreshCRMCouponPopWindowVC *viewController = 
    [FNFreshMrFreshCRMCouponPopWindowVC instanceWithNewGuidanceModel:model handle:^(BOOL dismiss) {
        if (dismiss) {

            [weakSelf presentNewGuidanceViewController];
        } else {
            
            if ([FNFreshUser shareInstance].isLogin) {
                
                UIViewController *viewController =
                [[FNMediator sharedInstance] freshCouponModel_FNFreshCouponViewController_initWithCouponClass:FNFreshCouponClassCoupon];
                [FNFreshTabBarController pushViewController:viewController animated:YES];
            } else {
                [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
                    [[FNFreshTabBarController shareInstance] switchTabBarWithParam:@{@"tabItemIndex":@4}];
                }];
            }
        }
    }];
    
    CGFloat width = ceilf(328.f);
    CGFloat height = 296;
    CGFloat count = model.dataModelArray.count;
    CGFloat collectionViewHeight = MIN(74.f*count - 8.f, 176.f);
    height += collectionViewHeight;
    [self fn_presentViewController:viewController 
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                          viewSize:CGSizeMake(width, height)
                          duration:0.75
                             alpha:0.5
                            handle:nil];
}


// 爆红包弹框
- (void)presentCouponBombViewControllerWithModel:(FNMrFreshPopWindowBombCouponModel *)model 
                                       isBombing:(BOOL)isBombing
{
    WS(weakSelf)
    if (model.couponsList.count == 1) {
        FNMrFreshPopupWindowNewGuidanceItemModel *nModel = [model.couponsList safeObjectAtIndex:0];
        nModel.couponImg = model.headImg;

        FNFreshMrFreshBombVoucherViewController *vc =
        [FNFreshMrFreshBombVoucherViewController initWithType:isBombing?FNFreshMrFreshBombingType:FNFreshMrFreshBombDefaultType
                                                    andSource:FNFreshMrFreshBombSourceHome
                                                      andData:nModel
                                                      handler:^(FNFreshMrFreshCouponBombPoint point) {
            switch (point) {
                case FNFreshMrFreshCouponBombExposure:
                    [FNFreshAgent eventWithTrackDataPrameters: @{
                        @"page_col":@"198003",
                        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                        @"track_type":@"6",
                    }];
                    break;
                    
                case FNFreshMrFreshCouponBombImmediately: {
                    [FNFreshAgent eventWithTrackDataPrameters:@{
                        @"page_col":@"198006",
                        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                        @"track_type":@"2",
                    }];
                    UIViewController *viewController =
                    [[FNMediator sharedInstance] freshCouponModel_FNFreshCouponViewController_initWithCouponClass:FNFreshCouponClassCoupon];
                    [FNFreshTabBarController pushViewController:viewController animated:YES];
                }
                    break;
                    
                case FNFreshMrFreshCouponBombClick:
                    [FNFreshAgent eventWithTrackDataPrameters:@{
                        @"page_col":@"198004",
                        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                        @"track_type":@"2",
                        @"col_position":@"1"
                    }];
                    break;
                    
                case FNFreshMrFreshCouponBombSuccess:
                    [FNFreshAgent eventWithTrackDataPrameters:@{
                        @"page_col":@"198005",
                        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                        @"track_type":@"6",
                    }];
                    break;
                    
                case FNFreshMrFreshCouponBombDismiss:

                    break;
                    
                default:
                    break;
            }
        }];
        
        vc.isFromMutiBomb = isBombing;
        CGFloat width = SCREEN_WIDTH;
        CGFloat height = 460;
        //        __weak typeof(vc)weakVC = vc;
        [self fn_presentViewController:vc
                     customAnimateType:FNFreshMrFreshCustomAnimateTypeFadeInFadeOut
                              viewSize:CGSizeMake(width, height)
                              duration:0.5
                                 alpha:0.5
                                handle:nil];

    } else {
        FNFreshMrFreshMutiBombVoucherViewController *vc = [FNFreshMrFreshMutiBombVoucherViewController initWithData:model handler:^(BOOL dismiss, NSInteger index) {
            
            if (dismiss) {

                [weakSelf presentNewGuidanceViewController];
            } else {
                model.couponsList = [NSArray arrayWithObject:[model.couponsList safeObjectAtIndex:index]];
                [weakSelf presentCouponBombViewControllerWithModel:model isBombing:YES];
            }
        }];
        
        CGFloat width = SCREEN_WIDTH;
        NSInteger count = MIN(model.couponsList.count, 5);
        CGFloat collectionHeight = (65 + 6) * count - 6;
        CGFloat height = 167 + collectionHeight + 14 + 28;
        //        __weak typeof(vc)weakVC = vc;
        [self fn_presentViewController:vc
                     customAnimateType:FNFreshMrFreshCustomAnimateTypeFadeInFadeOut
                              viewSize:CGSizeMake(width, height)
                              duration:0.5
                                 alpha:0.5
                                handle:nil];
    }
}

//新手指导
- (void)presentNewGuidanceViewController {
    
    if ([[FNCacheManager shareMananger] firstOpenForPage:NSStringFromClass([self class])] ||
        !self.isCurrentPage || !self.emptyNavBgView.hidden) {
        
        return;
    }
    
    [[FNCacheManager shareMananger] setFirstOpenedForPage:NSStringFromClass([self class])];
    self.addressTooltipView.hidden = YES;
    WS(weakSelf)
    FNFreshMrFreshNewGuidanceViewController *viewController =
    [FNFreshMrFreshNewGuidanceViewController instanceWithTopSpace:0 
                                                    showMsgCenter:self.viewModel.responseModel.showMsgCenter
                                                          handler:^{
        if (weakSelf.saleIndexPath) {
            [weakSelf showSaleNewGuideView:0];
        }
        
        [weakSelf showStoreListSwitchImgTips];
        [weakSelf showAddressWarning];
    }];
    
    CGSize size = CGSizeMake(SCREEN_WIDTH, SCREEN_HEIGHT);
    [self fn_presentViewController:viewController 
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeNone
                          viewSize:size
                          duration:0
                             alpha:0
                            handle:nil];
}

//版本更新
- (void)presentVersionUpdateControllerWithUpdateResponseModel:(FNFreshMrFreshVersionUpdateResponseModel *)updateResponseModel {
    
    __weak typeof(self)weakSelf = self;
    FNFreshMrFreshVersionUpdateViewController *viewController = 
    [FNFreshMrFreshVersionUpdateViewController instanceWithVersionUpdateResponseModel:updateResponseModel
                                                                              handler:^{
        weakSelf.versionUpdateFinish = YES;
        [weakSelf showAddressWarning];
        if (!weakSelf.isHandlePopwindow) {
            [weakSelf requestPopupWindowInfo];
        }
    }];
    
    [self fn_presentViewController:viewController
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFadeInFadeOut
                          viewSize:[UIScreen mainScreen].bounds.size
                          duration:0.25
                             alpha:0.5
                            handle:nil];
}

//欧尚合并公告
- (void)presentACToGoAnnouncementVCWithResponseModel:(FNFreshMrFreshACAnnouncementResponseModel *)response {
    
    if (!self.isCurrentPage) {
        return;
    }
    
    WS(weakSelf)
    FNFreshMrFreshH5AnnouncementViewController *vc = 
    [FNFreshMrFreshH5AnnouncementViewController instanceWithData:response handler:^(BOOL dismiss) {
        if (dismiss) {
            [weakSelf presentNewGuidanceViewController];
        }
    }];
    
    CGRect rect = [response.attributeContentDesc boundingRectWithSize:CGSizeMake(240, CGFLOAT_MAX)
                                                              options:NSStringDrawingUsesLineFragmentOrigin|NSStringDrawingUsesFontLeading
                                                              context:nil];
    CGFloat height = MIN(ceilf(rect.size.height), 250.f);
    if ([[FNCacheManager shareMananger] firstOpenForPage:FNMrFreshACToGoAnnouncement]) {
        height += 65 + 118;
    } else {
        height += 65 + 74;
    }
    CGSize size = CGSizeMake(280, height);
    [self fn_presentViewController:vc 
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFadeInFadeOut
                          viewSize:size
                          duration:0.25
                             alpha:0.5
                            handle:nil];
}

- (void)presentOldNForOneVCWithResponseModel:(FNMrFreshPopupOldNForOneModel *)response {
    WS(weakSelf);
    FNFreshMrFreshCustomerNXOneViewController *vc = [[FNFreshMrFreshCustomerNXOneViewController alloc] initWithData:response selectHandler:^{
        [weakSelf reloadOldNXOneCell];
    }];
    [self fn_presentViewController:vc
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFadeInFadeOut
                          viewSize:[UIScreen mainScreen].bounds.size
                          duration:0.5
                             alpha:0.75
                            handle:nil];
}

// push权限弹框，优先级最低，每次安装app后检查权限，仅弹一次
- (void)openRemotePushNot 
{
    if ([[FNCacheManager shareMananger] firstOpenForPage:FNMrFreshFirstEnterAppPushNot] ||
        ![[FNCacheManager shareMananger] firstOpenForPage:NSStringFromClass([self class])] ||
        !self.isCurrentPage || !self.emptyNavBgView.hidden) {
        // 满足以上条件不弹框
        return;
    }

    //弹框
    if ([[UIApplication sharedApplication] currentUserNotificationSettings].types == UIRemoteNotificationTypeNone) {
        [[FNCacheManager shareMananger] setFirstOpenedForPage:FNMrFreshFirstEnterAppPushNot];
        
        FNFreshNotificationPopWidowViewController *vc = [FNFreshNotificationPopWidowViewController instanceWithOpenCallBack:^{
            
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
            
        }];
        
        CGSize size = CGSizeMake(SCREEN_WIDTH, 280);
        [self fn_presentViewController:vc
                     customAnimateType:FNFreshMrFreshCustomAnimateTypeFadeInFadeOut
                              viewSize:size
                              duration:0.25
                                 alpha:0.5
                                handle:nil];
    }
}

// 判断是否同一天
- (BOOL)isSameDay:(NSDate *)date1 and:(NSDate *)date2
{
    NSCalendar *currentCalendar = [NSCalendar currentCalendar];
    unsigned unitFlags = NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay;
    NSDateComponents *comp1 = [currentCalendar components:unitFlags fromDate:date1];
    NSDateComponents *comp2 = [currentCalendar components:unitFlags fromDate:date2];
    return comp1.day == comp2.day && comp1.month == comp2.month && comp1.year == comp2.year;
}

@end
