<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina5_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FNFreshMrFreshViewController">
            <connections>
                <outlet property="abnormalNavigationBar" destination="JO6-OZ-j33" id="7mu-2q-2Mw"/>
                <outlet property="abnormalNavigationViewHeightConstraint" destination="sId-RS-g1z" id="YP5-SD-lvW"/>
                <outlet property="addressLab" destination="X9S-l8-rXA" id="wlM-vh-5Ns"/>
                <outlet property="bgImgView" destination="6pL-bA-BUv" id="f3n-NN-lhf"/>
                <outlet property="bgImgViewHeightConstraint" destination="XYX-aW-OfT" id="I2F-M5-8U9"/>
                <outlet property="bgImgViewTopConstraint" destination="zKx-hR-htk" id="df7-7c-aCA"/>
                <outlet property="changeStoreView" destination="KaU-aE-47q" id="a3u-29-UB4"/>
                <outlet property="collectionView" destination="3a4-Ri-XqQ" id="8cw-Ov-cit"/>
                <outlet property="collectionViewTopConstraint" destination="0mN-Up-m8c" id="Kot-Cr-y6o"/>
                <outlet property="configBgView" destination="WP5-6M-xrG" id="EgG-gr-ut2"/>
                <outlet property="configBgViewHeightConstriant" destination="ZCh-9f-kjy" id="Ut1-Bq-WH5"/>
                <outlet property="configBgViewTopConstraint" destination="p50-g4-Omq" id="lSN-tf-c1y"/>
                <outlet property="emptyAccessoryLabel" destination="Y7i-Pc-DUF" id="WxS-iq-tho"/>
                <outlet property="emptyNavBgView" destination="Ovt-V9-KHx" id="IAH-PI-pPn"/>
                <outlet property="emptyNavBgViewHeightConstraint" destination="ZGB-r6-l1S" id="d52-M5-6zS"/>
                <outlet property="emptyNavTitleLab" destination="j2S-OK-aYr" id="FAQ-bD-xsV"/>
                <outlet property="keywordLabel" destination="xno-R1-FmP" id="n6v-FU-ZUU"/>
                <outlet property="keywordsTabelView" destination="3FE-MQ-OVw" id="SW6-at-Whp"/>
                <outlet property="locationView" destination="bzG-Gr-cG2" id="gek-to-39y"/>
                <outlet property="messageCountLab" destination="rs5-1F-C6S" id="vDi-qT-XuS"/>
                <outlet property="messageCountWidthConstraint" destination="644-Sw-Mwx" id="SWb-x2-iHv"/>
                <outlet property="messageView" destination="zLX-7P-BAs" id="aZT-Hx-OEi"/>
                <outlet property="normalNavigationBar" destination="YFO-vm-YQM" id="Z7h-R6-dkQ"/>
                <outlet property="normalNavigationViewHeightConstraint" destination="pRI-eg-bWE" id="MWY-gD-OD0"/>
                <outlet property="qualificationView" destination="8cU-Yl-ir5" id="RWw-jG-66N"/>
                <outlet property="qualificationViewBottomConstraint" destination="aTb-ky-OG6" id="3BF-vs-0Za"/>
                <outlet property="restContentViewTopConstraint" destination="4gs-8A-9k5" id="Mc1-AL-hL1"/>
                <outlet property="restLabel" destination="pk2-20-5OX" id="0j1-l9-1xy"/>
                <outlet property="restNameLabel" destination="sH1-8r-Nk2" id="eg1-tf-xGf"/>
                <outlet property="restView" destination="AxO-t4-Xwj" id="4Gm-hV-HCw"/>
                <outlet property="restViewHeightConstraint" destination="MKL-kQ-k8B" id="bR3-b4-3YK"/>
                <outlet property="searchBackgroundView" destination="YMB-k0-rCg" id="WbY-0U-XyJ"/>
                <outlet property="searchBtn" destination="M6U-TP-flP" id="UQr-6S-Mk7"/>
                <outlet property="searchView" destination="V9l-o3-Mju" id="VDS-bc-dkA"/>
                <outlet property="storeNameDynamicScrollView" destination="y9f-ON-0aM" id="lv1-xw-8S4"/>
                <outlet property="storeNameScrollViewWidthConstraint" destination="suU-zP-qLy" id="Sl9-XV-3tw"/>
                <outlet property="switchStoreListImg" destination="GQj-tX-jfT" id="3IS-gP-53x"/>
                <outlet property="view" destination="6FW-1L-GFC" id="Tmm-dR-Nvv"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="6FW-1L-GFC">
            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WP5-6M-xrG">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="414"/>
                    <color key="backgroundColor" red="0.90196078430000004" green="0.0" blue="0.070588235289999995" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="414" id="ZCh-9f-kjy"/>
                    </constraints>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="6pL-bA-BUv">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="500"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="500" id="XYX-aW-OfT"/>
                    </constraints>
                </imageView>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AxO-t4-Xwj" customClass="FNFreshMrFreshGradientView">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="0.0"/>
                    <subviews>
                        <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="drV-hR-cgW">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="0.0"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg_rest" translatesAutoresizingMaskIntoConstraints="NO" id="5ay-qn-2fe">
                                    <rect key="frame" x="89.666666666666686" y="-35" width="235" height="70"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="门店休息提醒" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sH1-8r-Nk2">
                                    <rect key="frame" x="12" y="3" width="390" height="23"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="19"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pk2-20-5OX">
                                    <rect key="frame" x="12" y="-22.333333333333332" width="390" height="14.333333333333334"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="pk2-20-5OX" secondAttribute="trailing" constant="12" id="CrU-Yo-Z2I"/>
                                <constraint firstAttribute="bottom" secondItem="pk2-20-5OX" secondAttribute="bottom" constant="8" id="Gfu-3R-jK3"/>
                                <constraint firstItem="5ay-qn-2fe" firstAttribute="centerY" secondItem="drV-hR-cgW" secondAttribute="centerY" id="KdV-YN-NCO"/>
                                <constraint firstAttribute="trailing" secondItem="sH1-8r-Nk2" secondAttribute="trailing" constant="12" id="USh-Bg-edb"/>
                                <constraint firstItem="pk2-20-5OX" firstAttribute="leading" secondItem="drV-hR-cgW" secondAttribute="leading" constant="12" id="Zaj-G0-pKa"/>
                                <constraint firstItem="5ay-qn-2fe" firstAttribute="centerX" secondItem="drV-hR-cgW" secondAttribute="centerX" id="alY-0V-jzo"/>
                                <constraint firstItem="sH1-8r-Nk2" firstAttribute="top" secondItem="drV-hR-cgW" secondAttribute="top" constant="3" id="rXv-vc-UdD"/>
                                <constraint firstItem="pk2-20-5OX" firstAttribute="top" secondItem="sH1-8r-Nk2" secondAttribute="bottom" priority="750" constant="5" id="tZZ-oi-vO6"/>
                                <constraint firstItem="sH1-8r-Nk2" firstAttribute="leading" secondItem="drV-hR-cgW" secondAttribute="leading" constant="12" id="zYn-Eg-H14"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    <constraints>
                        <constraint firstItem="drV-hR-cgW" firstAttribute="top" secondItem="AxO-t4-Xwj" secondAttribute="top" priority="751" constant="20" id="4gs-8A-9k5"/>
                        <constraint firstItem="drV-hR-cgW" firstAttribute="leading" secondItem="AxO-t4-Xwj" secondAttribute="leading" id="C6c-lq-t7G"/>
                        <constraint firstAttribute="bottom" secondItem="drV-hR-cgW" secondAttribute="bottom" id="EJU-l4-3ph"/>
                        <constraint firstAttribute="height" priority="752" id="MKL-kQ-k8B"/>
                        <constraint firstAttribute="trailing" secondItem="drV-hR-cgW" secondAttribute="trailing" id="g0f-P9-VQX"/>
                    </constraints>
                </view>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="3a4-Ri-XqQ" customClass="FNFreshMrFreshBaseCollectionView">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="JqB-Q1-sZV" customClass="FNFreshMrFreshCollectionViewFlowLayout">
                        <size key="itemSize" width="50" height="50"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="aXK-jw-sTN"/>
                        <outlet property="delegate" destination="-1" id="vvQ-nX-QWa"/>
                    </connections>
                </collectionView>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JO6-OZ-j33">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jNO-Tp-7hv">
                            <rect key="frame" x="9" y="10" width="203.66666666666666" height="32"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_location_01" translatesAutoresizingMaskIntoConstraints="NO" id="ppj-DR-qnY">
                                    <rect key="frame" x="0.0" y="4.6666666666666643" width="23" height="23"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="无法获取定位，请手动切换" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Y7i-Pc-DUF">
                                    <rect key="frame" x="28" y="8.6666666666666679" width="147.66666666666666" height="14.666666666666668"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_address_down" translatesAutoresizingMaskIntoConstraints="NO" id="te2-yO-QkX">
                                    <rect key="frame" x="183.66666666666666" y="12.666666666666666" width="12" height="6.6666666666666661"/>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                            <constraints>
                                <constraint firstItem="te2-yO-QkX" firstAttribute="leading" secondItem="Y7i-Pc-DUF" secondAttribute="trailing" constant="8" id="ASd-B6-OO7"/>
                                <constraint firstItem="ppj-DR-qnY" firstAttribute="leading" secondItem="jNO-Tp-7hv" secondAttribute="leading" id="DXf-ad-DoJ"/>
                                <constraint firstAttribute="trailing" secondItem="te2-yO-QkX" secondAttribute="trailing" constant="8" id="LVm-Zn-57B"/>
                                <constraint firstItem="ppj-DR-qnY" firstAttribute="centerY" secondItem="jNO-Tp-7hv" secondAttribute="centerY" id="So4-A6-soc"/>
                                <constraint firstItem="te2-yO-QkX" firstAttribute="centerY" secondItem="jNO-Tp-7hv" secondAttribute="centerY" id="c3r-TD-i4t"/>
                                <constraint firstAttribute="height" constant="32" id="gFT-hi-M06"/>
                                <constraint firstItem="Y7i-Pc-DUF" firstAttribute="leading" secondItem="ppj-DR-qnY" secondAttribute="trailing" constant="5" id="ssz-d3-Zs6"/>
                                <constraint firstItem="Y7i-Pc-DUF" firstAttribute="centerY" secondItem="jNO-Tp-7hv" secondAttribute="centerY" id="uIU-pn-3de"/>
                            </constraints>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="jMk-rQ-5ZJ" appends="YES" id="cwY-N4-weO"/>
                                <outletCollection property="gestureRecognizers" destination="hMs-5D-Ljb" appends="YES" id="qGn-3a-2X7"/>
                            </connections>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="jNO-Tp-7hv" secondAttribute="bottom" constant="2" id="9sL-Ds-Wcx"/>
                        <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="jNO-Tp-7hv" secondAttribute="trailing" constant="9" id="M9x-iG-Oka"/>
                        <constraint firstItem="jNO-Tp-7hv" firstAttribute="leading" secondItem="JO6-OZ-j33" secondAttribute="leading" constant="9" id="qPc-Ui-KGf"/>
                        <constraint firstAttribute="height" constant="44" id="sId-RS-g1z"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YFO-vm-YQM">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="83"/>
                    <subviews>
                        <view alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YMB-k0-rCg" customClass="FNFreshMrFreshGradientView">
                            <rect key="frame" x="0.0" y="0.0" width="414" height="83"/>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bzG-Gr-cG2">
                            <rect key="frame" x="12" y="9" width="61" height="25"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_location_index" translatesAutoresizingMaskIntoConstraints="NO" id="hid-bO-gf4">
                                    <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="X9S-l8-rXA">
                                    <rect key="frame" x="25" y="12.666666666666668" width="0.0" height="0.0"/>
                                    <constraints>
                                        <constraint firstAttribute="width" relation="lessThanOrEqual" constant="125" id="bDc-sX-cfr"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="17"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y9f-ON-0aM" customClass="FNHomeVerticalScrollTextView">
                                    <rect key="frame" x="29" y="2.6666666666666661" width="20" height="19.666666666666671"/>
                                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="19.5" id="CWP-U9-H6G"/>
                                        <constraint firstAttribute="width" constant="20" id="suU-zP-qLy"/>
                                    </constraints>
                                </view>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_address_arrow" translatesAutoresizingMaskIntoConstraints="NO" id="BHe-3y-IUP">
                                    <rect key="frame" x="55" y="10.666666666666668" width="6" height="4"/>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="hid-bO-gf4" firstAttribute="leading" secondItem="bzG-Gr-cG2" secondAttribute="leading" id="442-Px-77z"/>
                                <constraint firstAttribute="trailing" secondItem="BHe-3y-IUP" secondAttribute="trailing" id="6rO-wk-gbn"/>
                                <constraint firstItem="BHe-3y-IUP" firstAttribute="leading" secondItem="y9f-ON-0aM" secondAttribute="trailing" constant="6" id="8Eu-l4-4zj"/>
                                <constraint firstItem="BHe-3y-IUP" firstAttribute="centerY" secondItem="bzG-Gr-cG2" secondAttribute="centerY" id="Few-16-9DU"/>
                                <constraint firstItem="y9f-ON-0aM" firstAttribute="centerY" secondItem="bzG-Gr-cG2" secondAttribute="centerY" id="INe-UU-ee3"/>
                                <constraint firstItem="X9S-l8-rXA" firstAttribute="centerY" secondItem="bzG-Gr-cG2" secondAttribute="centerY" id="OLj-md-Vh1"/>
                                <constraint firstItem="hid-bO-gf4" firstAttribute="centerY" secondItem="bzG-Gr-cG2" secondAttribute="centerY" id="Z4G-p8-QaG"/>
                                <constraint firstItem="y9f-ON-0aM" firstAttribute="leading" secondItem="X9S-l8-rXA" secondAttribute="trailing" constant="4" id="pBn-ge-ljA"/>
                                <constraint firstItem="X9S-l8-rXA" firstAttribute="leading" secondItem="hid-bO-gf4" secondAttribute="trailing" id="pZc-hx-Fyr"/>
                                <constraint firstAttribute="height" constant="25" id="s0Z-CX-DBd"/>
                            </constraints>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="nqh-s1-zdi" appends="YES" id="weg-Bb-Hz5"/>
                                <outletCollection property="gestureRecognizers" destination="UmH-Nr-TgP" appends="YES" id="dOV-W6-s8s"/>
                            </connections>
                        </view>
                        <view multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="V9l-o3-Mju" userLabel="searchView">
                            <rect key="frame" x="12" y="46" width="390" height="30"/>
                            <subviews>
                                <imageView clipsSubviews="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_index_code" translatesAutoresizingMaskIntoConstraints="NO" id="9cP-XU-69v">
                                    <rect key="frame" x="10" y="2.6666666666666643" width="25" height="25"/>
                                    <connections>
                                        <outletCollection property="gestureRecognizers" destination="wHe-bx-afQ" appends="YES" id="Ubb-sn-DPm"/>
                                    </connections>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bnb-nb-Ex5">
                                    <rect key="frame" x="45" y="7" width="1" height="16"/>
                                    <color key="backgroundColor" red="0.73333333333333328" green="0.73333333333333328" blue="0.73333333333333328" alpha="1" colorSpace="calibratedRGB"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="1" id="b5l-An-rLB"/>
                                        <constraint firstAttribute="height" constant="16" id="tWI-rc-MRE"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xno-R1-FmP">
                                    <rect key="frame" x="56" y="15" width="0.0" height="0.0"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" style="plain" separatorStyle="none" rowHeight="30" estimatedRowHeight="30" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="3FE-MQ-OVw" customClass="FNHomeSearchTableView">
                                    <rect key="frame" x="52" y="0.0" width="333" height="30"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <connections>
                                        <outlet property="dataSource" destination="-1" id="3JJ-IV-VfX"/>
                                        <outlet property="delegate" destination="-1" id="JTd-aD-gnr"/>
                                    </connections>
                                </tableView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M6U-TP-flP" customClass="FNHomeSearchButton">
                                    <rect key="frame" x="338" y="2" width="50" height="26"/>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="26" id="1lG-2T-y9B"/>
                                        <constraint firstAttribute="width" constant="50" id="Gxi-qu-AB7"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                    <color key="tintColor" red="0.90196078430000004" green="0.0" blue="0.070588235289999995" alpha="1" colorSpace="calibratedRGB"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" title="搜索">
                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </state>
                                    <state key="selected" title="搜索">
                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </state>
                                    <state key="highlighted" title="搜索">
                                        <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                            <real key="value" value="13"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <gestureRecognizers/>
                            <constraints>
                                <constraint firstItem="bnb-nb-Ex5" firstAttribute="leading" secondItem="9cP-XU-69v" secondAttribute="trailing" constant="10" id="GVe-eE-ohm"/>
                                <constraint firstItem="3FE-MQ-OVw" firstAttribute="leading" secondItem="bnb-nb-Ex5" secondAttribute="trailing" constant="6" id="N1h-c2-g6w"/>
                                <constraint firstItem="xno-R1-FmP" firstAttribute="leading" secondItem="bnb-nb-Ex5" secondAttribute="trailing" constant="10" id="Ri5-ID-Afx"/>
                                <constraint firstAttribute="bottom" secondItem="3FE-MQ-OVw" secondAttribute="bottom" id="WeO-Gi-jhW"/>
                                <constraint firstAttribute="trailing" secondItem="3FE-MQ-OVw" secondAttribute="trailing" constant="5" id="bht-mH-ySf"/>
                                <constraint firstItem="bnb-nb-Ex5" firstAttribute="centerY" secondItem="V9l-o3-Mju" secondAttribute="centerY" id="ca9-fK-hwh"/>
                                <constraint firstItem="xno-R1-FmP" firstAttribute="centerY" secondItem="V9l-o3-Mju" secondAttribute="centerY" id="d40-rq-sxe"/>
                                <constraint firstItem="M6U-TP-flP" firstAttribute="centerY" secondItem="V9l-o3-Mju" secondAttribute="centerY" id="eO8-Z6-eRn"/>
                                <constraint firstItem="9cP-XU-69v" firstAttribute="centerY" secondItem="V9l-o3-Mju" secondAttribute="centerY" id="k9W-wk-3aQ"/>
                                <constraint firstItem="9cP-XU-69v" firstAttribute="leading" secondItem="V9l-o3-Mju" secondAttribute="leading" constant="10" id="laG-Sj-16g"/>
                                <constraint firstAttribute="trailing" secondItem="M6U-TP-flP" secondAttribute="trailing" constant="2" id="oFh-dm-aEf"/>
                                <constraint firstItem="3FE-MQ-OVw" firstAttribute="top" secondItem="V9l-o3-Mju" secondAttribute="top" id="rBb-jn-o29"/>
                                <constraint firstAttribute="height" constant="30" id="yDk-to-NbC"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <real key="value" value="15"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <view hidden="YES" contentMode="scaleToFill" placeholderIntrinsicWidth="30" placeholderIntrinsicHeight="41" translatesAutoresizingMaskIntoConstraints="NO" id="zLX-7P-BAs">
                            <rect key="frame" x="377" y="-3" width="30" height="41"/>
                            <subviews>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_index_news" translatesAutoresizingMaskIntoConstraints="NO" id="hDa-PM-eyR">
                                    <rect key="frame" x="0.0" y="6" width="25" height="25"/>
                                </imageView>
                                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rs5-1F-C6S">
                                    <rect key="frame" x="12" y="0.0" width="18" height="18"/>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="18" id="644-Sw-Mwx"/>
                                        <constraint firstAttribute="height" constant="18" id="lSV-Xw-XeG"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="12"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="9"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                        <userDefinedRuntimeAttribute type="string" keyPath="fn_textColorName" value="fnbadge"/>
                                    </userDefinedRuntimeAttributes>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="消息" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gLG-W2-qCv">
                                    <rect key="frame" x="3.3333333333333144" y="32" width="18.666666666666668" height="9"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="9" id="mmm-J7-hiN"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="9"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="gLG-W2-qCv" firstAttribute="centerX" secondItem="hDa-PM-eyR" secondAttribute="centerX" id="1hx-kP-H3V"/>
                                <constraint firstAttribute="trailing" secondItem="hDa-PM-eyR" secondAttribute="trailing" constant="5" id="4I9-Go-0eo"/>
                                <constraint firstAttribute="bottom" secondItem="gLG-W2-qCv" secondAttribute="bottom" id="Okb-Vi-C5Q"/>
                                <constraint firstItem="hDa-PM-eyR" firstAttribute="leading" secondItem="zLX-7P-BAs" secondAttribute="leading" id="QXB-O2-GdH"/>
                                <constraint firstAttribute="trailing" secondItem="rs5-1F-C6S" secondAttribute="trailing" id="Rjl-zJ-Of2"/>
                                <constraint firstItem="hDa-PM-eyR" firstAttribute="top" secondItem="zLX-7P-BAs" secondAttribute="top" constant="6" id="V2j-tF-F31"/>
                                <constraint firstItem="rs5-1F-C6S" firstAttribute="top" secondItem="zLX-7P-BAs" secondAttribute="top" id="bDP-QU-bOo"/>
                                <constraint firstItem="gLG-W2-qCv" firstAttribute="top" secondItem="hDa-PM-eyR" secondAttribute="bottom" constant="1" id="f8d-R8-NRC"/>
                            </constraints>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="hji-93-qoo" appends="YES" id="yvM-xY-dfA"/>
                            </connections>
                        </view>
                        <view hidden="YES" contentMode="scaleToFill" placeholderIntrinsicWidth="37" placeholderIntrinsicHeight="35" translatesAutoresizingMaskIntoConstraints="NO" id="KaU-aE-47q">
                            <rect key="frame" x="329" y="3" width="37" height="35"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_store_toggle" translatesAutoresizingMaskIntoConstraints="NO" id="lwY-Ji-U1Y">
                                    <rect key="frame" x="6.6666666666666856" y="0.0" width="24" height="25"/>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="切换门店" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rgV-ED-6Mq">
                                    <rect key="frame" x="0.0" y="26" width="37" height="9"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="9" id="3o5-Jw-8xx"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="9"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="rgV-ED-6Mq" secondAttribute="bottom" id="Sqh-75-2Bg"/>
                                <constraint firstAttribute="trailing" secondItem="rgV-ED-6Mq" secondAttribute="trailing" id="WSM-3S-SuT"/>
                                <constraint firstItem="lwY-Ji-U1Y" firstAttribute="centerX" secondItem="KaU-aE-47q" secondAttribute="centerX" id="dnc-sR-1mk"/>
                                <constraint firstItem="lwY-Ji-U1Y" firstAttribute="top" secondItem="KaU-aE-47q" secondAttribute="top" id="hb0-71-o6f"/>
                                <constraint firstItem="rgV-ED-6Mq" firstAttribute="top" secondItem="lwY-Ji-U1Y" secondAttribute="bottom" constant="1" id="iwD-uh-0gD"/>
                                <constraint firstItem="rgV-ED-6Mq" firstAttribute="leading" secondItem="KaU-aE-47q" secondAttribute="leading" id="rF9-V5-vHV"/>
                            </constraints>
                            <connections>
                                <outletCollection property="gestureRecognizers" destination="AEq-Td-9DN" appends="YES" id="HzG-SO-awA"/>
                            </connections>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="V9l-o3-Mju" firstAttribute="top" secondItem="zLX-7P-BAs" secondAttribute="bottom" constant="8" id="4iQ-Je-FkM"/>
                        <constraint firstItem="V9l-o3-Mju" firstAttribute="top" secondItem="bzG-Gr-cG2" secondAttribute="bottom" constant="12" id="A25-fY-cQY"/>
                        <constraint firstAttribute="trailing" secondItem="zLX-7P-BAs" secondAttribute="trailing" constant="7" id="Akd-sp-07G"/>
                        <constraint firstItem="YMB-k0-rCg" firstAttribute="leading" secondItem="YFO-vm-YQM" secondAttribute="leading" id="Ko5-D1-1dW"/>
                        <constraint firstItem="bzG-Gr-cG2" firstAttribute="leading" secondItem="YFO-vm-YQM" secondAttribute="leading" constant="12" id="PZq-7M-wEq"/>
                        <constraint firstAttribute="trailing" secondItem="YMB-k0-rCg" secondAttribute="trailing" id="cTx-iy-sPb"/>
                        <constraint firstAttribute="bottom" secondItem="V9l-o3-Mju" secondAttribute="bottom" constant="7" id="cmf-1i-pia"/>
                        <constraint firstItem="YMB-k0-rCg" firstAttribute="top" secondItem="YFO-vm-YQM" secondAttribute="top" id="eAY-Wu-7ob"/>
                        <constraint firstAttribute="trailing" secondItem="KaU-aE-47q" secondAttribute="trailing" constant="48" id="hrl-rk-ala"/>
                        <constraint firstItem="V9l-o3-Mju" firstAttribute="leading" secondItem="YFO-vm-YQM" secondAttribute="leading" constant="12" id="j6H-ik-jsQ"/>
                        <constraint firstAttribute="bottom" secondItem="YMB-k0-rCg" secondAttribute="bottom" id="osv-nV-6WD"/>
                        <constraint firstAttribute="height" constant="83" id="pRI-eg-bWE"/>
                        <constraint firstAttribute="trailing" secondItem="V9l-o3-Mju" secondAttribute="trailing" constant="12" id="vmr-Qi-RZI"/>
                        <constraint firstItem="KaU-aE-47q" firstAttribute="bottom" secondItem="zLX-7P-BAs" secondAttribute="bottom" id="wMn-Hw-oev"/>
                    </constraints>
                </view>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ovt-V9-KHx" customClass="FNFreshMrFreshGradientView">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="大润发优鲜" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="j2S-OK-aYr">
                            <rect key="frame" x="161" y="12" width="92" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="qgF-rE-qIp"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="j2S-OK-aYr" firstAttribute="centerX" secondItem="Ovt-V9-KHx" secondAttribute="centerX" id="JMP-G1-DzA"/>
                        <constraint firstAttribute="height" constant="44" id="ZGB-r6-l1S"/>
                        <constraint firstAttribute="bottom" secondItem="j2S-OK-aYr" secondAttribute="bottom" constant="12" id="lOz-nK-br7"/>
                    </constraints>
                </view>
                <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_switch_tips" translatesAutoresizingMaskIntoConstraints="NO" id="GQj-tX-jfT">
                    <rect key="frame" x="268" y="40" width="98" height="83"/>
                    <constraints>
                        <constraint firstAttribute="width" secondItem="GQj-tX-jfT" secondAttribute="height" multiplier="98:83" id="Wlq-OC-tWb"/>
                        <constraint firstAttribute="width" constant="98" id="ohY-UH-USV"/>
                    </constraints>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_pic_zz" translatesAutoresizingMaskIntoConstraints="NO" id="8cU-Yl-ir5">
                    <rect key="frame" x="397" y="592" width="17" height="57"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="17" id="0Vg-ya-Ray"/>
                        <constraint firstAttribute="height" constant="57" id="5VQ-ED-Jj2"/>
                    </constraints>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TIX-W7-EAs">
                    <rect key="frame" x="397" y="592" width="17" height="57"/>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <connections>
                        <action selector="gotoAboutUsVC:" destination="-1" eventType="touchUpInside" id="lmU-I3-Erq"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.94901960784313721" green="0.94901960784313721" blue="0.94901960784313721" alpha="1" colorSpace="calibratedRGB"/>
            <gestureRecognizers/>
            <constraints>
                <constraint firstItem="TIX-W7-EAs" firstAttribute="leading" secondItem="8cU-Yl-ir5" secondAttribute="leading" id="0Od-bU-dMd"/>
                <constraint firstItem="3a4-Ri-XqQ" firstAttribute="top" secondItem="AxO-t4-Xwj" secondAttribute="bottom" id="0mN-Up-m8c"/>
                <constraint firstItem="AxO-t4-Xwj" firstAttribute="leading" secondItem="6FW-1L-GFC" secondAttribute="leading" id="0pJ-f2-OjL"/>
                <constraint firstItem="TIX-W7-EAs" firstAttribute="top" secondItem="8cU-Yl-ir5" secondAttribute="top" id="120-Jc-siE"/>
                <constraint firstItem="GQj-tX-jfT" firstAttribute="top" secondItem="KaU-aE-47q" secondAttribute="bottom" constant="2" id="74F-VI-BAm"/>
                <constraint firstAttribute="trailing" secondItem="AxO-t4-Xwj" secondAttribute="trailing" id="8dm-du-Rbp"/>
                <constraint firstAttribute="trailing" secondItem="3a4-Ri-XqQ" secondAttribute="trailing" id="DuA-1o-EfV"/>
                <constraint firstItem="JO6-OZ-j33" firstAttribute="top" secondItem="AxO-t4-Xwj" secondAttribute="bottom" id="EYa-nK-nUd"/>
                <constraint firstItem="YFO-vm-YQM" firstAttribute="leading" secondItem="6FW-1L-GFC" secondAttribute="leading" id="FcV-TC-Psb"/>
                <constraint firstItem="AxO-t4-Xwj" firstAttribute="top" secondItem="6FW-1L-GFC" secondAttribute="top" id="KAS-JR-u6H"/>
                <constraint firstItem="WP5-6M-xrG" firstAttribute="leading" secondItem="6FW-1L-GFC" secondAttribute="leading" id="LCL-om-vxx"/>
                <constraint firstAttribute="trailing" secondItem="YFO-vm-YQM" secondAttribute="trailing" id="LKP-0k-hxf"/>
                <constraint firstAttribute="bottom" secondItem="3a4-Ri-XqQ" secondAttribute="bottom" id="Q2R-Am-4TU"/>
                <constraint firstItem="TIX-W7-EAs" firstAttribute="trailing" secondItem="8cU-Yl-ir5" secondAttribute="trailing" id="SPx-S3-uBy"/>
                <constraint firstItem="Ovt-V9-KHx" firstAttribute="leading" secondItem="6FW-1L-GFC" secondAttribute="leading" id="UZr-3C-WXt"/>
                <constraint firstItem="TIX-W7-EAs" firstAttribute="bottom" secondItem="8cU-Yl-ir5" secondAttribute="bottom" id="WId-kN-2lP"/>
                <constraint firstAttribute="bottom" secondItem="8cU-Yl-ir5" secondAttribute="bottom" constant="87" id="aTb-ky-OG6"/>
                <constraint firstAttribute="trailing" secondItem="6pL-bA-BUv" secondAttribute="trailing" id="dKc-cU-oTW"/>
                <constraint firstAttribute="trailing" secondItem="Ovt-V9-KHx" secondAttribute="trailing" id="guM-UA-bVY"/>
                <constraint firstAttribute="trailing" secondItem="8cU-Yl-ir5" secondAttribute="trailing" id="hVH-e2-jrZ"/>
                <constraint firstItem="Ovt-V9-KHx" firstAttribute="top" secondItem="AxO-t4-Xwj" secondAttribute="bottom" id="jMA-Wj-B6q"/>
                <constraint firstItem="GQj-tX-jfT" firstAttribute="trailing" secondItem="KaU-aE-47q" secondAttribute="trailing" id="lld-8n-556"/>
                <constraint firstItem="JO6-OZ-j33" firstAttribute="leading" secondItem="6FW-1L-GFC" secondAttribute="leading" id="n9g-sD-HcS"/>
                <constraint firstItem="WP5-6M-xrG" firstAttribute="top" secondItem="AxO-t4-Xwj" secondAttribute="bottom" id="p50-g4-Omq"/>
                <constraint firstItem="3a4-Ri-XqQ" firstAttribute="leading" secondItem="6FW-1L-GFC" secondAttribute="leading" id="tB2-F8-yug"/>
                <constraint firstItem="6pL-bA-BUv" firstAttribute="leading" secondItem="6FW-1L-GFC" secondAttribute="leading" id="txh-ea-szx"/>
                <constraint firstAttribute="trailing" secondItem="WP5-6M-xrG" secondAttribute="trailing" id="xAo-VS-cvG"/>
                <constraint firstItem="YFO-vm-YQM" firstAttribute="top" secondItem="AxO-t4-Xwj" secondAttribute="bottom" id="yht-hd-IR9"/>
                <constraint firstItem="6pL-bA-BUv" firstAttribute="top" secondItem="AxO-t4-Xwj" secondAttribute="bottom" id="zKx-hR-htk"/>
                <constraint firstAttribute="trailing" secondItem="JO6-OZ-j33" secondAttribute="trailing" id="zar-g0-Kii"/>
            </constraints>
            <point key="canvasLocation" x="-3157" y="-1357"/>
        </view>
        <tapGestureRecognizer id="nqh-s1-zdi">
            <connections>
                <action selector="presentPositioningViewController:" destination="-1" id="jXh-Qy-6jU"/>
            </connections>
        </tapGestureRecognizer>
        <tapGestureRecognizer id="jMk-rQ-5ZJ">
            <connections>
                <action selector="presentPositioningViewController:" destination="-1" id="6b1-oF-gdh"/>
            </connections>
        </tapGestureRecognizer>
        <tapGestureRecognizer id="hji-93-qoo">
            <connections>
                <action selector="presentMessageCenterViewController:" destination="-1" id="HsU-nf-xgd"/>
            </connections>
        </tapGestureRecognizer>
        <tapGestureRecognizer id="wHe-bx-afQ">
            <connections>
                <action selector="gotoScanCoupon:" destination="-1" id="dj6-ox-vdQ"/>
            </connections>
        </tapGestureRecognizer>
        <tapGestureRecognizer id="AEq-Td-9DN">
            <connections>
                <action selector="switchStoreList:" destination="-1" id="SKP-q1-eRg"/>
            </connections>
        </tapGestureRecognizer>
        <pongPressGestureRecognizer allowableMovement="10" minimumPressDuration="0.5" id="UmH-Nr-TgP">
            <connections>
                <action selector="chooseStoreList:" destination="-1" id="wVM-In-oBx"/>
            </connections>
        </pongPressGestureRecognizer>
        <pongPressGestureRecognizer allowableMovement="10" minimumPressDuration="0.5" id="hMs-5D-Ljb">
            <connections>
                <action selector="chooseStoreList:" destination="-1" id="y3U-Pt-ymD"/>
            </connections>
        </pongPressGestureRecognizer>
    </objects>
    <resources>
        <image name="bg_rest" width="235" height="70"/>
        <image name="home_address_arrow" width="6" height="4"/>
        <image name="home_pic_zz" width="17" height="57"/>
        <image name="home_switch_tips" width="97.666664123535156" height="82.666664123535156"/>
        <image name="icon_address_down" width="12" height="6.6666665077209473"/>
        <image name="icon_index_code" width="25" height="25"/>
        <image name="icon_index_news" width="25" height="25"/>
        <image name="icon_location_01" width="23" height="23"/>
        <image name="icon_location_index" width="25" height="25"/>
        <image name="icon_store_toggle" width="24" height="24"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
