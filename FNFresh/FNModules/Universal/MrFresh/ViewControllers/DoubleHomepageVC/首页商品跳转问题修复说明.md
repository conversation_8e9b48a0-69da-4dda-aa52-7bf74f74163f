# 首页商品跳转问题修复说明

## 问题描述

在首页点击商品后，偶发出现无法正常push到商详页的现象：
- 点击首页tab后，再点击商品，在确认商详的vc和navigationController存在的情况下，没有push到商详的vc
- 再次点击，还是没有push到商详
- 切换到其他tab，再切换到首页tab，或者刷新下首页，就能正常跳转了

## 问题根因分析

### 核心问题：`isCurrentPage` 属性判断逻辑导致页面跳转失效

**问题代码位置**：
```objc
// FNFreshMrFreshViewController.m 第2128-2130行
- (BOOL)isCurrentPage {
    return _currentPage && ![FNFreshTabBarController shareInstance].navigationController.hasPresentedViewController;
}
```

### 问题触发流程：

1. **首页弹窗触发**：当首页请求弹窗数据并使用 `fn_presentViewController` 方法present弹窗时
2. **状态标记变更**：`hasPresentedViewController` 被设置为 `YES`
3. **isCurrentPage失效**：导致 `isCurrentPage` 返回 `NO`
4. **跳转逻辑受阻**：在某些依赖 `isCurrentPage` 状态的逻辑中，商品点击跳转被阻止或延迟
5. **状态未及时重置**：弹窗dismiss后，相关状态没有及时重置，导致后续点击仍然失效

### 影响范围：

- `FNFreshMrFreshFeedsGoodsViewController` 中的商品点击跳转
- `FNFreshMrFreshSingleFeedsGoodsVC` 中的商品点击跳转  
- 菜谱详情页跳转
- 鲜月票页面跳转

## 解决方案

### 修复策略：在商品点击跳转时增加容错机制

**核心思路**：
1. 确保跳转在主线程执行
2. 检查navigationController可用性
3. 添加重试机制处理临时状态异常

### 具体修改

#### 1. 修复 `FNFreshMrFreshFeedsGoodsViewController.m`

**修改位置1**：`freshFeedsClickGoodsLookDetail` 方法（第1022-1030行）
```objc
// 修复：确保在主线程中执行跳转，并添加延迟以避免弹窗状态冲突
dispatch_async(dispatch_get_main_queue(), ^{
    // 检查navigationController是否存在
    if ([FNFreshTabBarController shareInstance].navigationController) {
        [[FNFreshTabBarController shareInstance] pushViewController:detailVC animated:YES];
    } else {
        // 如果navigationController暂时不可用，稍后重试
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[FNFreshTabBarController shareInstance] pushViewController:detailVC animated:YES];
        });
    }
});
```

**修改位置2**：`collectionView:didSelectItemAtIndexPath:` 方法中的菜谱和鲜月票跳转（第931-963行）

#### 2. 修复 `FNFreshMrFreshSingleFeedsGoodsVC.m`

**修改位置**：`freshFeedsClickGoodsLookDetail:rank:isLeft:` 方法（第375-386行）

### 修复效果

1. **主线程保证**：确保所有UI操作在主线程执行
2. **状态检查**：在跳转前检查navigationController可用性
3. **重试机制**：当navigationController暂时不可用时，延迟0.1秒重试
4. **兼容性**：不影响正常情况下的跳转逻辑

## 测试建议

### 测试场景

1. **正常跳转测试**：验证修复后正常商品点击跳转功能
2. **弹窗冲突测试**：
   - 触发首页弹窗
   - 在弹窗显示期间点击商品
   - 验证是否能正常跳转
3. **连续点击测试**：快速连续点击商品，验证跳转稳定性
4. **Tab切换测试**：在不同tab间切换后点击商品，验证跳转正常

### 验证要点

- 商品详情页能正常打开
- 菜谱详情页能正常打开  
- 鲜月票页面能正常打开
- 不会出现重复跳转
- 不会出现卡死或崩溃

## 后续优化建议

1. **根本性解决**：考虑重构 `isCurrentPage` 的判断逻辑，避免弹窗状态影响页面跳转
2. **状态管理优化**：在弹窗dismiss时及时重置相关状态标记
3. **统一跳转接口**：考虑在 `FNFreshTabBarController` 中提供更健壮的跳转方法
4. **监控机制**：添加跳转失败的监控和日志，便于后续问题排查
